{"__meta": {"id": "X368685bd066b7e0eb76ec50d1a60060e", "datetime": "2025-08-08 18:43:43", "utime": **********.825827, "method": "GET", "uri": "/annex/public/loan/fresh_applicants", "ip": "**************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754671422.881822, "end": **********.825843, "duration": 0.9440209865570068, "duration_str": "944ms", "measures": [{"label": "Booting", "start": 1754671422.881822, "relative_start": 0, "end": **********.578782, "relative_end": **********.578782, "duration": 0.6969599723815918, "duration_str": "697ms", "params": [], "collector": null}, {"label": "Application", "start": **********.581114, "relative_start": 0.6992919445037842, "end": **********.825844, "relative_end": 9.5367431640625e-07, "duration": 0.24472999572753906, "duration_str": "245ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 48294856, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "loan::themes.adminlte.loan.fresh_applicants (\\Modules\\Loan\\Resources\\views\\themes\\adminlte\\loan\\fresh_applicants.blade.php)", "param_count": 1, "params": ["data"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\resources\\views\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "core::layouts.master (\\themes\\adminlte\\views\\layouts\\master.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.top_nav (\\themes\\adminlte\\views\\partials\\top_nav.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::menu.admin (\\themes\\adminlte\\views\\menu\\admin.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.flash.message (\\themes\\adminlte\\views\\partials\\flash\\message.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.footer (\\themes\\adminlte\\views\\partials\\footer.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}]}, "route": {"uri": "GET loan/fresh_applicants", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanController@fresh_applicants", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:244-290"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.04714000000000001, "accumulated_duration_str": "47.14ms", "statements": [{"sql": "select * from `users` where `id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.01705, "duration_str": "17.05ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 10 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["10", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select count(*) as aggregate from (select concat(clients.first_name,' ',clients.last_name) client,concat(users.first_name,' ',users.last_name) loan_officer,loans.id,loans.client_id,loans.applied_amount,loans.principal,loans.disbursed_on_date,loans.expected_maturity_date,loan_products.name loan_product,loans.status,loans.decimals,branches.name branch, SUM(loan_repayment_schedules.principal) total_principal, SUM(loan_repayment_schedules.principal_written_off_derived) principal_written_off_derived, SUM(loan_repayment_schedules.principal_repaid_derived) principal_repaid_derived, SUM(loan_repayment_schedules.interest) total_interest, SUM(loan_repayment_schedules.interest_waived_derived) interest_waived_derived,SUM(loan_repayment_schedules.interest_written_off_derived) interest_written_off_derived,  SUM(loan_repayment_schedules.interest_repaid_derived) interest_repaid_derived,SUM(loan_repayment_schedules.fees) total_fees, SUM(loan_repayment_schedules.fees_waived_derived) fees_waived_derived, SUM(loan_repayment_schedules.fees_written_off_derived) fees_written_off_derived, SUM(loan_repayment_schedules.fees_repaid_derived) fees_repaid_derived,SUM(loan_repayment_schedules.penalties) total_penalties, SUM(loan_repayment_schedules.penalties_waived_derived) penalties_waived_derived, SUM(loan_repayment_schedules.penalties_written_off_derived) penalties_written_off_derived, SUM(loan_repayment_schedules.penalties_repaid_derived) penalties_repaid_derived from `loans` left join `clients` on `clients`.`id` = `loans`.`client_id` left join `loan_repayment_schedules` on `loan_repayment_schedules`.`loan_id` = `loans`.`id` left join `loan_products` on `loan_products`.`id` = `loans`.`loan_product_id` left join `branches` on `branches`.`id` = `loans`.`branch_id` left join `users` on `users`.`id` = `loans`.`loan_officer_id` where `loans`.`status` = 'pending' or `loans`.`status` = 'submitted' or `loans`.`status` = 'approved' group by `loans`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["pending", "submitted", "approved"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 287}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.01595, "duration_str": "15.95ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:287", "connection": "annex"}, {"sql": "select concat(clients.first_name,' ',clients.last_name) client,concat(users.first_name,' ',users.last_name) loan_officer,loans.id,loans.client_id,loans.applied_amount,loans.principal,loans.disbursed_on_date,loans.expected_maturity_date,loan_products.name loan_product,loans.status,loans.decimals,branches.name branch, SUM(loan_repayment_schedules.principal) total_principal, SUM(loan_repayment_schedules.principal_written_off_derived) principal_written_off_derived, SUM(loan_repayment_schedules.principal_repaid_derived) principal_repaid_derived, SUM(loan_repayment_schedules.interest) total_interest, SUM(loan_repayment_schedules.interest_waived_derived) interest_waived_derived,SUM(loan_repayment_schedules.interest_written_off_derived) interest_written_off_derived,  SUM(loan_repayment_schedules.interest_repaid_derived) interest_repaid_derived,SUM(loan_repayment_schedules.fees) total_fees, SUM(loan_repayment_schedules.fees_waived_derived) fees_waived_derived, SUM(loan_repayment_schedules.fees_written_off_derived) fees_written_off_derived, SUM(loan_repayment_schedules.fees_repaid_derived) fees_repaid_derived,SUM(loan_repayment_schedules.penalties) total_penalties, SUM(loan_repayment_schedules.penalties_waived_derived) penalties_waived_derived, SUM(loan_repayment_schedules.penalties_written_off_derived) penalties_written_off_derived, SUM(loan_repayment_schedules.penalties_repaid_derived) penalties_repaid_derived from `loans` left join `clients` on `clients`.`id` = `loans`.`client_id` left join `loan_repayment_schedules` on `loan_repayment_schedules`.`loan_id` = `loans`.`id` left join `loan_products` on `loan_products`.`id` = `loans`.`loan_product_id` left join `branches` on `branches`.`id` = `loans`.`branch_id` left join `users` on `users`.`id` = `loans`.`loan_officer_id` where `loans`.`status` = 'pending' or `loans`.`status` = 'submitted' or `loans`.`status` = 'approved' group by `loans`.`id` limit 20 offset 0", "type": "query", "params": [], "bindings": ["pending", "submitted", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 287}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00228, "duration_str": "2.28ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:287", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.00429, "duration_str": "4.29ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 10 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["10", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 10 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["10", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 10 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["10", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 30}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `notifications` where `notifications`.`notifiable_id` = 10 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["10", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::partials.top_nav", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "view::core::partials.top_nav:31", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_logo' limit 1", "type": "query", "params": [], "bindings": ["core.company_logo"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 3}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "view::core::menu.admin:3", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_name' limit 1", "type": "query", "params": [], "bindings": ["core.company_name"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 8}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "view::core::menu.admin:8", "connection": "annex"}, {"sql": "select * from `menus` where `is_parent` = 1 order by `menu_order` asc", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}, {"sql": "select * from `menus` where `menus`.`parent_id` in (1, 9, 10, 12, 13, 17, 18, 21, 24, 28, 31, 36, 37, 44, 49, 54, 58, 61, 66, 71) order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}]}, "models": {"data": {"Modules\\Core\\Entities\\Menu": 73, "Modules\\Setting\\Entities\\Setting": 2, "Modules\\Loan\\Entities\\Loan": 4, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 1}, "count": 81}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<PERSON><PERSON><PERSON>@mjy3.com\"\n  \"user\" => array:25 [\n    \"id\" => 10\n    \"created_by_id\" => null\n    \"branch_id\" => null\n    \"name\" => \"\"\n    \"username\" => null\n    \"email\" => \"<PERSON>a<PERSON>@mjy3.com\"\n    \"email_verified_at\" => \"2025-01-16T03:14:17.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"<PERSON><PERSON>\"\n    \"last_name\" => \"Eshun\"\n    \"phone\" => \"0539862144\"\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"female\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => \"All information on this data base is strictly confidential and for internal use only.\"\n    \"photo\" => \"T7Bw0DyVu5aY3P7deOzEBPE24SSqQaob2r9gRRPQ.jpeg\"\n    \"created_at\" => \"2025-01-16T03:14:17.000000Z\"\n    \"updated_at\" => \"2025-01-16T23:28:11.000000Z\"\n    \"full_name\" => \"<PERSON><PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 4\n        \"is_system\" => 0\n        \"name\" => \"Loan Officer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"updated_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 10\n          \"role_id\" => 4\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n    \"unread_notifications\" => []\n  ]\n]", "api": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 46, "messages": [{"message": "[ability => loan.loans.create, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2087577744 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087577744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.735689}, {"message": "[ability => loan.loans.edit, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1634859828 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">loan.loans.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634859828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.738319}, {"message": "[ability => loan.loans.destroy, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1575917680 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">loan.loans.destroy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575917680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.739674}, {"message": "[ability => loan.loans.edit, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-377916825 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">loan.loans.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377916825\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.741089}, {"message": "[ability => loan.loans.destroy, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-314805056 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">loan.loans.destroy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314805056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.742353}, {"message": "[ability => loan.loans.edit, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1008474117 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">loan.loans.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008474117\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.743808}, {"message": "[ability => loan.loans.destroy, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-594745437 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">loan.loans.destroy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594745437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.74521}, {"message": "[ability => dashboard.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dashboard.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768964}, {"message": "[\n  ability => accounting.chart_of_accounts.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"34 characters\">accounting.chart_of_accounts.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.770689}, {"message": "[\n  ability => accounting.journal_entries.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">accounting.journal_entries.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.771935}, {"message": "[ability => branch.branches.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">branch.branches.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773215}, {"message": "[\n  ability => branch.branches.create,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-182712348 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">branch.branches.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182712348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.774434}, {"message": "[ability => client.clients.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1634981537 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">client.clients.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634981537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775877}, {"message": "[ability => client.clients.create, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1145729815 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">client.clients.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145729815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.777283}, {"message": "[ability => payroll.payroll.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-228270654 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll.payroll.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228270654\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.778497}, {"message": "[\n  ability => communication.campaigns.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-250592692 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">communication.campaigns.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250592692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.779707}, {"message": "[\n  ability => communication.campaigns.create,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1341354370 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">communication.campaigns.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341354370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.7809}, {"message": "[\n  ability => communication.logs.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-806845613 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">communication.logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806845613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.782096}, {"message": "[\n  ability => communication.sms_gateways.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2120002452 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">communication.sms_gateways.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120002452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.783254}, {"message": "[ability => loan.loans.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1755446993 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755446993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.784466}, {"message": "[ability => loan.loans.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1039554449 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039554449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.785666}, {"message": "[ability => loan.loans.create, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-538551860 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538551860\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.786867}, {"message": "[\n  ability => loan.loans.products.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-324878753 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324878753\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.788078}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-48383675 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48383675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789253}, {"message": "[ability => loan.loans.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-259532699 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259532699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.790448}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1644989449 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644989449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.791884}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-380794242 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-380794242\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.793094}, {"message": "[ability => reports.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1581516155 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reports.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581516155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.794307}, {"message": "[\n  ability => expense.expenses.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-327095702 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">expense.expenses.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327095702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.795524}, {"message": "[ability => savings.savings.index, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1010316911 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">savings.savings.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010316911\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.796724}, {"message": "[\n  ability => savings.savings.create,\n  result => true,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-353981038 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">savings.savings.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353981038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.797929}, {"message": "[\n  ability => savings.savings.products.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1558069290 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">savings.savings.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558069290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.799132}, {"message": "[\n  ability => savings.savings.charges.index,\n  result => true,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-573315802 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">savings.savings.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573315802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.800303}, {"message": "[\n  ability => customfield.custom_fields.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-341891288 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">customfield.custom_fields.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341891288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.801521}, {"message": "[\n  ability => customfield.custom_fields.create,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2081795363 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customfield.custom_fields.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081795363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.802696}, {"message": "[ability => income.income.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1030164277 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">income.income.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030164277\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.803867}, {"message": "[ability => user.users.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1334358352 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.users.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1334358352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.805076}, {"message": "[ability => user.users.create, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1396394267 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">user.users.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396394267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.806247}, {"message": "[ability => user.roles.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-726686695 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.roles.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726686695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.807667}, {"message": "[ability => core.modules.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-239290831 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">core.modules.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239290831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.808868}, {"message": "[ability => asset.assets.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1228438644 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">asset.assets.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228438644\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.81004}, {"message": "[ability => share.shares.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1471070931 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">share.shares.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471070931\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.811205}, {"message": "[ability => setting.setting.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-66890152 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66890152\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.812385}, {"message": "[ability => core.menu.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2020235150 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">core.menu.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020235150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.813579}, {"message": "[ability => core.themes.index, result => null, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1116611252 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">core.themes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116611252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.814755}, {"message": "[\n  ability => activitylog.activity_logs.index,\n  result => null,\n  user => 10,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2112731187 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">activitylog.activity_logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112731187\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.815931}]}, "session": {"_token": "OAYgquvl16WZFn5bSfhFjghMpIbfSyamH58qA6TF", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://*************/annex/public/loan/fresh_applicants\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "10", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://*************/annex/public/_debugbar/telescope/9f9620b1-0a51-4d2e-bb47-d13f53f229d1\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan/fresh_applicants", "status_code": "<pre class=sf-dump id=sf-dump-1525869163 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1525869163\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1124765896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1124765896\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-866191892 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-866191892\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://*************/annex/public/loan</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">jy3_logistics_session=eyJpdiI6ImxER0w2ZjNEUGNUOVlKd01MamZuR3c9PSIsInZhbHVlIjoiZTMzazVVTDRtb3Y3VWcvY1J1WXh4S2lhYTBPWkJ3YndQME14R2lRQldpb0lqQ3dhZmpJS3N0cGxUelFYcU15WDJqem9GZ1h5MldybnFRZ1RLRVBwZ1B5SWlSRDBJc3N1VmdqWG5LMTV1emVUVnhqc09OLzRQZ09YMkNCZnBiOHkiLCJtYWMiOiJjYWJjYzZkZTQzZmYxNTIxMTkxODUxN2ZhNDVhNjYyYzY4NDQzZGFhZmY3OWIzNWMxZjFmZjhiMmViMDVlODAzIn0%3D; XSRF-TOKEN=eyJpdiI6IkE1WDNDbnhSTzBzdjh0dEttdTd5cVE9PSIsInZhbHVlIjoieHBnK1pzYUVkWEtnTVM0cEJXL0ZsUkl1Tnpyc0VzRVdWdi9vd1JDNFhKeEd0bEdTMXg2clZuY0VzTFpLWmFqMTArL3F3VFM3eFpXSStwK0ViWEpwR09HSytPWDFIRVllcVJETnp0QlNFUGFxS0oxNWVndCtrczZhb1dwaTJRT0siLCJtYWMiOiJiNGU5NDE0MWM0YjgxNDNiOWNlMmFmYzYyNjlhOGIxYjM2NTMyMDk2MDk2NjFhY2EwYmY2YzQzZTk3MGVmODM0In0%3D; mjy3_micro_loans_session=eyJpdiI6IklmNUdhK1ZJbmlCMUgrbzdEek1vWmc9PSIsInZhbHVlIjoiL3Jsa2F3UWEyaVhmNGltUm5haUxSNTREckFyWEp3NXl3U2paeXBzQ0QrQ3pSUDdTeTAyOFFrL3UvbUxucXRjL0JFZzFDYnpIMnhYR0llbHlMUFYvNm5EQXBSZEJIYTdyYnZlbkErYlB2emlLN2p1Yk96dzhpQ1BKbFViZmNSRmgiLCJtYWMiOiIxZDQ4YTYwZTU0MDNkYmY5ZWY1OWNlODZjMzk1MDljNWY1MGM4NjM2ZjQ5NTE5YmI2ZTYwZTdjOTFlZmM0YzA3In0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-111785761 data-indent-pad=\"  \"><span class=sf-dump-note>array:50</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://*************/annex/public/loan</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1052 characters\">jy3_logistics_session=eyJpdiI6ImxER0w2ZjNEUGNUOVlKd01MamZuR3c9PSIsInZhbHVlIjoiZTMzazVVTDRtb3Y3VWcvY1J1WXh4S2lhYTBPWkJ3YndQME14R2lRQldpb0lqQ3dhZmpJS3N0cGxUelFYcU15WDJqem9GZ1h5MldybnFRZ1RLRVBwZ1B5SWlSRDBJc3N1VmdqWG5LMTV1emVUVnhqc09OLzRQZ09YMkNCZnBiOHkiLCJtYWMiOiJjYWJjYzZkZTQzZmYxNTIxMTkxODUxN2ZhNDVhNjYyYzY4NDQzZGFhZmY3OWIzNWMxZjFmZjhiMmViMDVlODAzIn0%3D; XSRF-TOKEN=eyJpdiI6IkE1WDNDbnhSTzBzdjh0dEttdTd5cVE9PSIsInZhbHVlIjoieHBnK1pzYUVkWEtnTVM0cEJXL0ZsUkl1Tnpyc0VzRVdWdi9vd1JDNFhKeEd0bEdTMXg2clZuY0VzTFpLWmFqMTArL3F3VFM3eFpXSStwK0ViWEpwR09HSytPWDFIRVllcVJETnp0QlNFUGFxS0oxNWVndCtrczZhb1dwaTJRT0siLCJtYWMiOiJiNGU5NDE0MWM0YjgxNDNiOWNlMmFmYzYyNjlhOGIxYjM2NTMyMDk2MDk2NjFhY2EwYmY2YzQzZTk3MGVmODM0In0%3D; mjy3_micro_loans_session=eyJpdiI6IklmNUdhK1ZJbmlCMUgrbzdEek1vWmc9PSIsInZhbHVlIjoiL3Jsa2F3UWEyaVhmNGltUm5haUxSNTREckFyWEp3NXl3U2paeXBzQ0QrQ3pSUDdTeTAyOFFrL3UvbUxucXRjL0JFZzFDYnpIMnhYR0llbHlMUFYvNm5EQXBSZEJIYTdyYnZlbkErYlB2emlLN2p1Yk96dzhpQ1BKbFViZmNSRmgiLCJtYWMiOiIxZDQ4YTYwZTU0MDNkYmY5ZWY1OWNlODZjMzk1MDljNWY1MGM4NjM2ZjQ5NTE5YmI2ZTYwZTdjOTFlZmM0YzA3In0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"99 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at ************* Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"14 characters\">**************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">34981</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/annex/public/loan/fresh_applicants</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/annex/public/loan/fresh_applicants</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754671422.8818</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754671422</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111785761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-414954205 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp>\n  \"<span class=sf-dump-key>jy3_logistics_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OAYgquvl16WZFn5bSfhFjghMpIbfSyamH58qA6TF</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nN11lQvsyoUgXImmoJdavcedd7Gu6PSjd9QBx8bH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-414954205\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-995482096 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 16:43:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6InRSSWREaW84dWpJQjhjbnVkZzUwcUE9PSIsInZhbHVlIjoiWFlkSDh1RjhZL3NxOGMxYUI1MkVGTW5JYmFBcnNhaDVFWDE4bjhyTEFyOE1hSWtHWFFzcmhQK2dkdkhWckhwQlR5T1FhNjN0UDdhYlhPWUF6WkVDcWZYN1FscUZiSEJibHhPSUNxM0s1cmJmZHpveEFYaEZCRndWT2FCYUwvZWsiLCJtYWMiOiI1MzkzMWQ0ZDAzZWZjMjU2MmI2YjRlNjEzNjM0OTJmMDcxN2MxZjg4ZjgzOTU1YjdmZjhhOTM4ODFhYTlkYzRhIn0%3D; expires=Sat, 09-Aug-2025 12:43:43 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6InF4WVl1akphNkEzOUpXMGJ2NXdDeEE9PSIsInZhbHVlIjoiR1ZOUDl4T0RIcmh4TWlaRnEyazFsQlUydFJaSFdVVHNPOEFYaGNwUWlhVGZWK0JMcXR6SHY1WnQwMVlaVTl5MmJCOEpSU0RPN05yUU9qNHNibzZrYUVLMi9KakhYcWVwa1hGR09ESitrdGdEQVFVY1hPcllPWkZpcE5zZXkyQlgiLCJtYWMiOiI2N2I2M2RlYmE0N2RjNzFhNmIxNDQzNzA0MGIyMTFlOTc5MmZlYTlkMjA4NjRiOWFiYjc4NzFiYTRlMzlmMGQyIn0%3D; expires=Sat, 09-Aug-2025 12:43:43 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6InRSSWREaW84dWpJQjhjbnVkZzUwcUE9PSIsInZhbHVlIjoiWFlkSDh1RjhZL3NxOGMxYUI1MkVGTW5JYmFBcnNhaDVFWDE4bjhyTEFyOE1hSWtHWFFzcmhQK2dkdkhWckhwQlR5T1FhNjN0UDdhYlhPWUF6WkVDcWZYN1FscUZiSEJibHhPSUNxM0s1cmJmZHpveEFYaEZCRndWT2FCYUwvZWsiLCJtYWMiOiI1MzkzMWQ0ZDAzZWZjMjU2MmI2YjRlNjEzNjM0OTJmMDcxN2MxZjg4ZjgzOTU1YjdmZjhhOTM4ODFhYTlkYzRhIn0%3D; expires=Sat, 09-Aug-2025 12:43:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6InF4WVl1akphNkEzOUpXMGJ2NXdDeEE9PSIsInZhbHVlIjoiR1ZOUDl4T0RIcmh4TWlaRnEyazFsQlUydFJaSFdVVHNPOEFYaGNwUWlhVGZWK0JMcXR6SHY1WnQwMVlaVTl5MmJCOEpSU0RPN05yUU9qNHNibzZrYUVLMi9KakhYcWVwa1hGR09ESitrdGdEQVFVY1hPcllPWkZpcE5zZXkyQlgiLCJtYWMiOiI2N2I2M2RlYmE0N2RjNzFhNmIxNDQzNzA0MGIyMTFlOTc5MmZlYTlkMjA4NjRiOWFiYjc4NzFiYTRlMzlmMGQyIn0%3D; expires=Sat, 09-Aug-2025 12:43:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995482096\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-724216334 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OAYgquvl16WZFn5bSfhFjghMpIbfSyamH58qA6TF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://*************/annex/public/loan/fresh_applicants</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724216334\", {\"maxDepth\":0})</script>\n"}}