{"__meta": {"id": "X165e287a9c92bb831ea9d9542673e100", "datetime": "2025-08-08 17:56:51", "utime": 1754668611.615572, "method": "POST", "uri": "/annex/public/loan/guarantor/564/update", "ip": "*************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754668610.631836, "end": 1754668611.615591, "duration": 0.9837551116943359, "duration_str": "984ms", "measures": [{"label": "Booting", "start": 1754668610.631836, "relative_start": 0, "end": 1754668611.35452, "relative_end": 1754668611.35452, "duration": 0.7226841449737549, "duration_str": "723ms", "params": [], "collector": null}, {"label": "Application", "start": 1754668611.356825, "relative_start": 0.7249891757965088, "end": 1754668611.615594, "relative_end": 2.86102294921875e-06, "duration": 0.25876879692077637, "duration_str": "259ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 47055264, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST loan/guarantor/{id}/update", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanGuarantorController@update", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:162-233"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.10020000000000003, "accumulated_duration_str": "100ms", "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.07292, "duration_str": "72.92ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["1", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.00404, "duration_str": "4.04ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["1", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select * from `clients` where `clients`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 173}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `loan_guarantors` where `loan_guarantors`.`id` = '564' limit 1", "type": "query", "params": [], "bindings": ["564"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 174}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `custom_fields` where `custom_fields`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 199}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where (`category` = 'add_guarantor' and `parent_id` = 564 and `custom_field_id` = 21) limit 1", "type": "query", "params": [], "bindings": ["add_guarantor", "564", "21"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 20, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 222}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0059299999999999995, "duration_str": "5.93ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "insert into `custom_fields_meta` (`category`, `parent_id`, `custom_field_id`, `value`, `updated_at`, `created_at`) values ('add_guarantor', 564, 21, 'M2EctXJ1vZGzyaSywMJWfNS5YKVGH0MaUEiBydCD.png', '2025-08-08 17:56:51', '2025-08-08 17:56:51')", "type": "query", "params": [], "bindings": ["add_guarantor", "564", "21", "M2EctXJ1vZGzyaSywMJWfNS5YKVGH0MaUEiBydCD.png", "2025-08-08 17:56:51", "2025-08-08 17:56:51"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php", "line": 263}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 222}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}], "duration": 0.00453, "duration_str": "4.53ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:263", "connection": "annex"}, {"sql": "select * from `custom_fields` where `custom_fields`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 199}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where (`category` = 'add_guarantor' and `parent_id` = 564 and `custom_field_id` = 22) limit 1", "type": "query", "params": [], "bindings": ["add_guarantor", "564", "22"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 20, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 222}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0051600000000000005, "duration_str": "5.16ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "insert into `custom_fields_meta` (`category`, `parent_id`, `custom_field_id`, `value`, `updated_at`, `created_at`) values ('add_guarantor', 564, 22, 'EEBB2DCjspG6nQAG5eAnQC9OO8WoBnRJ0psRpzv1.png', '2025-08-08 17:56:51', '2025-08-08 17:56:51')", "type": "query", "params": [], "bindings": ["add_guarantor", "564", "22", "EEBB2DCjspG6nQAG5eAnQC9OO8WoBnRJ0psRpzv1.png", "2025-08-08 17:56:51", "2025-08-08 17:56:51"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php", "line": 263}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 222}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}], "duration": 0.00157, "duration_str": "1.57ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:263", "connection": "annex"}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `causer_id`, `causer_type`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('default', '{\\\"id\\\":564}', 1, '<PERSON><PERSON><PERSON>\\User\\Entities\\User', 564, 'Mo<PERSON><PERSON>\\Loan\\Entities\\LoanGuarantor', 'Update Loan Guarantor', '2025-08-08 17:56:51', '2025-08-08 17:56:51')", "type": "query", "params": [], "bindings": ["default", "{&quot;id&quot;:564}", "1", "Modules\\User\\Entities\\User", "564", "Modules\\Loan\\Entities\\LoanGuarantor", "Update <PERSON><PERSON>", "2025-08-08 17:56:51", "2025-08-08 17:56:51"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 161}, {"index": 16, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 230}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php:161", "connection": "annex"}]}, "models": {"data": {"Modules\\CustomField\\Entities\\CustomField": 2, "Modules\\Loan\\Entities\\LoanGuarantor": 1, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 1}, "count": 5}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"admin\"\n  \"user\" => array:24 [\n    \"id\" => 1\n    \"created_by_id\" => null\n    \"branch_id\" => 1\n    \"name\" => \"Admin\"\n    \"username\" => \"admin\"\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2022-06-06T13:23:25.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"Quintin\"\n    \"last_name\" => \"Kweku\"\n    \"phone\" => null\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"male\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => null\n    \"photo\" => \"mMuwmDyxpyNjnFBRkttEO1NRGogDJyT8gNGTiezP.jpeg\"\n    \"created_at\" => \"2022-06-06T13:23:25.000000Z\"\n    \"updated_at\" => \"2023-05-26T13:08:30.000000Z\"\n    \"full_name\" => \"Quin<PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 1\n        \"is_system\" => 1\n        \"name\" => \"admin\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T13:23:25.000000Z\"\n        \"updated_at\" => \"2022-06-06T13:23:25.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 1\n          \"role_id\" => 1\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n  ]\n]", "api": "null"}, "names": "web: admin"}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => loan.loans.guarantors.edit,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1949160308 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">loan.loans.guarantors.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949160308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754668611.542161}]}, "session": {"_token": "BDnw2z3HYFycnztWa5AjP1tqvRAJrGhgE6Cug2GV", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://mjy3.relandbek.com/annex/public/loan/guarantor/564/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"flash_notification\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "flash_notification": "Illuminate\\Support\\Collection {#2180\n  #items: array:1 [\n    0 => Laracasts\\Flash\\Message {#2179\n      +title: null\n      +message: \"Successfully Saved\"\n      +level: \"success\"\n      +important: true\n      +overlay: false\n    }\n  ]\n}", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"https://mjy3.relandbek.com/annex/public/_debugbar/telescope/9f960fed-f3fe-4b14-b84d-7601ebb51f4f\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan/guarantor/564/update", "status_code": "<pre class=sf-dump id=sf-dump-553138722 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-553138722\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1914064827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1914064827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-446240047 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BDnw2z3HYFycnztWa5AjP1tqvRAJrGhgE6Cug2GV</span>\"\n  \"<span class=sf-dump-key>is_client</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>client_relationship_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>title_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">FRANCIS</span>\"\n  \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">KOOMSON</span>\"\n  \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n  \"<span class=sf-dump-key>marital_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">single</span>\"\n  \"<span class=sf-dump-key>mobile</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0246159541</span>\"\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n  \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1989-03-02</span>\"\n  \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>profession_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"19 characters\">11F/20  AKOTOBINSIN</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>guaranteed_amount</span>\" => \"<span class=sf-dump-str title=\"11 characters\">5000.000000</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446240047\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1651697052 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4494</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://mjy3.relandbek.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryV9bfEwMiELBhN4Av</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">https://mjy3.relandbek.com/annex/public/loan/guarantor/564/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6Imo5SDgzemMvRHo4NFMyU01nNDkyMEE9PSIsInZhbHVlIjoiNWlRQk11NVF6NGhKZG1lWWp0Nzc0V29sOVMxYSthTnA3UzQzakQybzRlcFhFc1hUNHgzZTZEUkxmWDVTZ3RJcDU2VmlxbVk4bkFVb1orZThGSDc4bVVkMzc1b2IreTEzaWhZY21PYUVaSFFSbE5BTGhpRWVEQkZEU1RSeWs1NTciLCJtYWMiOiJhZmZhNTFhM2Q1MjYwYzNkMDk3ZTk4Nzk3NDk0Y2Q2NjllMWYyMzk0NmQ3ZDMzYmM2ZWYyYmIxOGNkN2NiY2I3In0%3D; mjy3_micro_loans_session=eyJpdiI6IjBhVVlhTFArMXQrTGVhbERzTjV5YWc9PSIsInZhbHVlIjoiRlVOSkhmdEIwK2hqMTE0aVhKMWdDR1g5VVJoNm1hM0ZwcDE0enMxWDAyOFEvdmtqOTM5WVp3NUlPcTFSSDZEeUNZc0ZKZzVjYW8wVTVnNmlaUXFheHRqQnRYTWRTY3BEdXVacEVzQUNINldGamthbGI5ZUIyYklFWDRVclcxSVgiLCJtYWMiOiJkYjAxZGI4M2IxMDE3MzdlMDBkZTZkMzE3NTk3OThkYTBkYWUxZDYzODJkNGMxY2E2YThkNDY0ZjcwMjcwYWIwIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651697052\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1501839766 data-indent-pad=\"  \"><span class=sf-dump-note>array:90</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_S_DN_CN</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_C</span>\" => \"<span class=sf-dump-str title=\"2 characters\">AT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_O</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ZeroSSL</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_CN</span>\" => \"<span class=sf-dump-str title=\"33 characters\">ZeroSSL RSA Domain Secure Site CA</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_SAN_DNS_0</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_VERSION_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">mod_ssl/2.4.48</span>\"\n  \"<span class=sf-dump-key>SSL_VERSION_LIBRARY</span>\" => \"<span class=sf-dump-str title=\"14 characters\">OpenSSL/1.1.1k</span>\"\n  \"<span class=sf-dump-key>SSL_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TLSv1.3</span>\"\n  \"<span class=sf-dump-key>SSL_SECURE_RENEG</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>SSL_COMPRESS_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">NULL</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">TLS_AES_256_GCM_SHA384</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_EXPORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_USEKEYSIZE</span>\" => \"<span class=sf-dump-str title=\"3 characters\">256</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_ALGKEYSIZE</span>\" => \"<span class=sf-dump-str title=\"3 characters\">256</span>\"\n  \"<span class=sf-dump-key>SSL_CLIENT_VERIFY</span>\" => \"<span class=sf-dump-str title=\"4 characters\">NONE</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_M_VERSION</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_M_SERIAL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">9915F93C899342A4AF7869D230B4DEA4</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_V_START</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Jul 29 00:00:00 2025 GMT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_V_END</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Oct 27 23:59:59 2025 GMT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_S_DN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">CN=mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN</span>\" => \"<span class=sf-dump-str title=\"51 characters\">CN=ZeroSSL RSA Domain Secure Site CA,O=ZeroSSL,C=AT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_A_KEY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_A_SIG</span>\" => \"<span class=sf-dump-str title=\"23 characters\">sha384WithRSAEncryption</span>\"\n  \"<span class=sf-dump-key>SSL_SESSION_ID</span>\" => \"<span class=sf-dump-str title=\"64 characters\">e1d84f3fa9dbc0221a4843f251de829b86e21b53ac72dc1ddedbc4659abc416e</span>\"\n  \"<span class=sf-dump-key>SSL_SESSION_RESUMED</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Resumed</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4494</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryV9bfEwMiELBhN4Av</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"63 characters\">https://mjy3.relandbek.com/annex/public/loan/guarantor/564/edit</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6Imo5SDgzemMvRHo4NFMyU01nNDkyMEE9PSIsInZhbHVlIjoiNWlRQk11NVF6NGhKZG1lWWp0Nzc0V29sOVMxYSthTnA3UzQzakQybzRlcFhFc1hUNHgzZTZEUkxmWDVTZ3RJcDU2VmlxbVk4bkFVb1orZThGSDc4bVVkMzc1b2IreTEzaWhZY21PYUVaSFFSbE5BTGhpRWVEQkZEU1RSeWs1NTciLCJtYWMiOiJhZmZhNTFhM2Q1MjYwYzNkMDk3ZTk4Nzk3NDk0Y2Q2NjllMWYyMzk0NmQ3ZDMzYmM2ZWYyYmIxOGNkN2NiY2I3In0%3D; mjy3_micro_loans_session=eyJpdiI6IjBhVVlhTFArMXQrTGVhbERzTjV5YWc9PSIsInZhbHVlIjoiRlVOSkhmdEIwK2hqMTE0aVhKMWdDR1g5VVJoNm1hM0ZwcDE0enMxWDAyOFEvdmtqOTM5WVp3NUlPcTFSSDZEeUNZc0ZKZzVjYW8wVTVnNmlaUXFheHRqQnRYTWRTY3BEdXVacEVzQUNINldGamthbGI5ZUIyYklFWDRVclcxSVgiLCJtYWMiOiJkYjAxZGI4M2IxMDE3MzdlMDBkZTZkMzE3NTk3OThkYTBkYWUxZDYzODJkNGMxY2E2YThkNDY0ZjcwMjcwYWIwIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"105 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at mjy3.relandbek.com Port 443&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12169</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"39 characters\">/annex/public/loan/guarantor/564/update</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"39 characters\">/annex/public/loan/guarantor/564/update</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754668610.6318</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754668610</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501839766\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1740120167 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BDnw2z3HYFycnztWa5AjP1tqvRAJrGhgE6Cug2GV</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LUvyKRf9TZjoIrXwRu9xc8Vt2J7ZVuxzNoCBChfI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740120167\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-536807345 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 15:56:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">https://mjy3.relandbek.com/annex/public/loan/697/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6IkExVnREbVArNXZZRmE1aEJoczF5MkE9PSIsInZhbHVlIjoiUHpNUHZrVk00UmxBKzd5eU5mMXQ2MGthTmdlMFRsVkxRUEcrZDhJYm1PV041V2paWTNnWVpQbGJnOWhUUzVFZjB5QWdDeERsTCsvcmhlajhTNkZGRzBPWjFXQUVhV2pOZVg0VjFMaEZDK01yVXIrR2dvaHdIWkNxdGpWZUdENVkiLCJtYWMiOiI3ZDBiMTY0ODIwZGUwY2EwNTc4OTU2N2VmMjNhOGU1ZTY4YzQxYzMwY2M0YTA0Nzk5ODljMTFkZmE4OGE5NDliIn0%3D; expires=Sat, 09-Aug-2025 11:56:51 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6Ikxsenp5ekFzci91QVhjRXJSeUVvQVE9PSIsInZhbHVlIjoiSXFkbkNBSlhUWTRSalJJRXdiMU52bkM2SEx6MU9XTlFLaThnK0pjL1NpRVoySkd1UmJra1VRQUcyU29rdVJmR3d0bEUrekZ4clpxQTlRblhLampITXQ3b1VuSzVENlZCblVmSEJIc2xtOWVoOW1ob3h5VU0xakNmdHBnQzMxL1UiLCJtYWMiOiI0NGVmMjA5OWFlODBiMDUzZGNmYWI2NmMxZTJjNDBjNjhiOTQ3MDFhNjgwODc3YWM3MWFjNmYzNjVmN2JkMTNjIn0%3D; expires=Sat, 09-Aug-2025 11:56:51 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6IkExVnREbVArNXZZRmE1aEJoczF5MkE9PSIsInZhbHVlIjoiUHpNUHZrVk00UmxBKzd5eU5mMXQ2MGthTmdlMFRsVkxRUEcrZDhJYm1PV041V2paWTNnWVpQbGJnOWhUUzVFZjB5QWdDeERsTCsvcmhlajhTNkZGRzBPWjFXQUVhV2pOZVg0VjFMaEZDK01yVXIrR2dvaHdIWkNxdGpWZUdENVkiLCJtYWMiOiI3ZDBiMTY0ODIwZGUwY2EwNTc4OTU2N2VmMjNhOGU1ZTY4YzQxYzMwY2M0YTA0Nzk5ODljMTFkZmE4OGE5NDliIn0%3D; expires=Sat, 09-Aug-2025 11:56:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6Ikxsenp5ekFzci91QVhjRXJSeUVvQVE9PSIsInZhbHVlIjoiSXFkbkNBSlhUWTRSalJJRXdiMU52bkM2SEx6MU9XTlFLaThnK0pjL1NpRVoySkd1UmJra1VRQUcyU29rdVJmR3d0bEUrekZ4clpxQTlRblhLampITXQ3b1VuSzVENlZCblVmSEJIc2xtOWVoOW1ob3h5VU0xakNmdHBnQzMxL1UiLCJtYWMiOiI0NGVmMjA5OWFlODBiMDUzZGNmYWI2NmMxZTJjNDBjNjhiOTQ3MDFhNjgwODc3YWM3MWFjNmYzNjVmN2JkMTNjIn0%3D; expires=Sat, 09-Aug-2025 11:56:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536807345\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1068513479 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BDnw2z3HYFycnztWa5AjP1tqvRAJrGhgE6Cug2GV</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">https://mjy3.relandbek.com/annex/public/loan/guarantor/564/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">flash_notification</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>flash_notification</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2180</a><samp>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Laracasts\\Flash\\Message\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Laracasts\\Flash</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Message</span> {<a class=sf-dump-ref>#2179</a><samp>\n        +<span class=sf-dump-public title=\"Public property\">title</span>: <span class=sf-dump-const>null</span>\n        +<span class=sf-dump-public title=\"Public property\">message</span>: \"<span class=sf-dump-str title=\"18 characters\">Successfully Saved</span>\"\n        +<span class=sf-dump-public title=\"Public property\">level</span>: \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        +<span class=sf-dump-public title=\"Public property\">important</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">overlay</span>: <span class=sf-dump-const>false</span>\n      </samp>}\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068513479\", {\"maxDepth\":0})</script>\n"}}