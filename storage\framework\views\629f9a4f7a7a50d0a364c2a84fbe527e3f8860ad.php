<?php $__env->startSection('title'); ?>
    <?php echo e($loan_guarantor->first_name); ?> <?php echo e($loan_guarantor->last_name); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-3">

            <!-- Profile Image -->
            <div class="box box-primary">
                <div class="box-body box-profile">
                    <?php if(!empty($loan_guarantor->photo)): ?>
                        <a href="<?php echo e(asset('storage/uploads/loans/'.$loan_guarantor->photo)); ?>"
                           class="fancybox">
                            <img
                                    class="profile-user-img img-fluid img-circle"
                                    src="<?php echo e(asset('storage/uploads/loans/'.$loan_guarantor->photo)); ?>"
                                    alt="User profile picture">
                        </a>
                    <?php else: ?>
                        <img class="profile-user-img img-responsive img-circle"
                             src="<?php echo e(asset('assets/dist/img/user.png')); ?>"
                             alt="User profile picture">
                    <?php endif; ?>
                    <h3 class="profile-username text-center">
                        <?php if(!empty($loan_guarantor->title)): ?>
                            <?php echo e($loan_guarantor->title->name); ?>

                        <?php endif; ?>
                        <?php echo e($loan_guarantor->first_name); ?> <?php echo e($loan_guarantor->last_name); ?></h3>

                    <p class="text-muted text-center">
                        <?php if(!empty($loan_guarantor->profession->name)): ?>
                            <?php echo e($loan_guarantor->profession->name); ?>

                        <?php endif; ?>
                    </p>

                    <ul class="list-group list-group-unbordered">
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('client::general.relationship',1)); ?></b>
                            <a class="pull-right">
                                <?php if(!empty($loan_guarantor->client_relationship)): ?>
                                    <?php echo e($loan_guarantor->client_relationship->name); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.mobile',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->mobile); ?>

                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.email',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->email); ?>

                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.dob',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->dob); ?>

                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.gender',1)); ?></b>
                            <a class="pull-right">
                                <?php if($loan_guarantor->gender=='male'): ?>
                                    <?php echo e(trans_choice('core::general.male',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->gender=='female'): ?>
                                    <?php echo e(trans_choice('core::general.female',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->gender=='unspecified'): ?>
                                    <?php echo e(trans_choice('core::general.unspecified',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->gender=='other'): ?>
                                    <?php echo e(trans_choice('core::general.other',1)); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('client::general.marital_status',1)); ?></b>
                            <a class="pull-right">
                                <?php if($loan_guarantor->marital_status=='single'): ?>
                                    <?php echo e(trans_choice('client::general.single',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='married'): ?>
                                    <?php echo e(trans_choice('client::general.married',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='divorced'): ?>
                                    <?php echo e(trans_choice('client::general.divorced',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='widowed'): ?>
                                    <?php echo e(trans_choice('client::general.widowed',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='other'): ?>
                                    <?php echo e(trans_choice('client::general.other',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='unspecified'): ?>
                                    <?php echo e(trans_choice('core::general.unspecified',1)); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.zip',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->zip); ?>

                            </a>
                        </li>
                    </ul>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->

            <!-- About Me Box -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><?php echo e(trans_choice('core::general.extra',1)); ?> <?php echo e(trans_choice('core::general.detail',2)); ?></h3>
                </div>
                <!-- /.box-header -->
                <div class="box-body">

                    <strong><i class="fa fa-map-marker margin-r-5"></i> <?php echo e(trans_choice('core::general.address',1)); ?>

                    </strong>

                    <p class="text-muted">
                        <?php echo e($loan_guarantor->address); ?><br>
                        <?php if(!empty($loan_guarantor->country)): ?>
                            <?php echo e($loan_guarantor->country->name); ?>

                        <?php endif; ?>
                    </p>
                    <hr>

                    <strong><i class="fa fa-file-text-o margin-r-5"></i> <?php echo e(trans_choice('core::general.note',2)); ?>

                    </strong>

                    <p> <?php echo e($loan_guarantor->notes); ?></p>

                    <?php if($custom_fields->count() > 0): ?>
                        <hr>
                        <strong><i class="fa fa-list margin-r-5"></i> <?php echo e(trans_choice('customfield::general.custom_field',2)); ?>

                        </strong>
                        <div class="row">
                            <?php $__currentLoopData = $custom_fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $custom_field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                $custom_field_meta = \Modules\CustomField\Entities\CustomFieldMeta::where('category', 'add_guarantor')
                                    ->where('parent_id', $loan_guarantor->id)
                                    ->where('custom_field_id', $custom_field->id)
                                    ->first();
                                ?>
                                <?php if(!empty($custom_field_meta)): ?>
                                    <div class="col-md-12">
                                        <p><strong><?php echo e($custom_field->name); ?>:</strong>
                                            <?php if($custom_field->type == 'file' && !empty($custom_field_meta->value)): ?>
                                                <a href="<?php echo e(asset('storage/uploads/custom_fields/'.$custom_field_meta->value)); ?>" target="_blank"><?php echo e($custom_field_meta->value); ?></a>
                                            <?php else: ?>
                                                <?php echo e($custom_field_meta->value); ?>

                                            <?php endif; ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->
        </div>
        <!-- /.col -->
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script>
        $('.data-table').DataTable({
            "language": {
                "lengthMenu": "<?php echo e(trans('general.lengthMenu')); ?>",
                "zeroRecords": "<?php echo e(trans('general.zeroRecords')); ?>",
                "info": "<?php echo e(trans('general.info')); ?>",
                "infoEmpty": "<?php echo e(trans('general.infoEmpty')); ?>",
                "search": "<?php echo e(trans('general.search')); ?>",
                "infoFiltered": "<?php echo e(trans('general.infoFiltered')); ?>",
                "paginate": {
                    "first": "<?php echo e(trans('general.first')); ?>",
                    "last": "<?php echo e(trans('general.last')); ?>",
                    "next": "<?php echo e(trans('general.next')); ?>",
                    "previous": "<?php echo e(trans('general.previous')); ?>"
                },
                "columnDefs": [
                    {"orderable": false, "targets": 0}
                ]
            },
            responsive: true
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('core::layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\annex\Modules\Loan\Providers/../Resources/views/themes/adminlte/guarantor/show.blade.php ENDPATH**/ ?>