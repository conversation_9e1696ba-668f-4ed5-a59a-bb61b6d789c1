<?php $__env->startSection('title'); ?>
    <?php echo e($loan_guarantor->first_name); ?> <?php echo e($loan_guarantor->last_name); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
<style>
    .custom-field-value {
        margin-top: 5px;
    }
    .guarantor-profile-img {
        max-width: 150px;
        max-height: 150px;
        border-radius: 50%;
    }
    .guarantor-status {
        font-size: 14px;
        margin-top: 10px;
    }
    .loan-info-table td {
        border: none;
        padding: 8px 12px;
    }
    .loan-info-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    @media (max-width: 768px) {
        .col-md-3, .col-md-9 {
            margin-bottom: 20px;
        }
    }
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-user"></i>
                        <?php echo e(trans_choice('loan::general.guarantor',1)); ?> <?php echo e(trans_choice('core::general.detail',2)); ?>

                        <?php if(!empty($loan_guarantor->title)): ?>
                            - <?php echo e($loan_guarantor->title->name); ?>

                        <?php endif; ?>
                        <?php echo e($loan_guarantor->first_name); ?> <?php echo e($loan_guarantor->last_name); ?>

                    </h3>
                    <div class="box-tools pull-right">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('loan.loans.guarantors.edit')): ?>
                            <a href="<?php echo e(url('loan/guarantor/'.$loan_guarantor->id.'/edit')); ?>" class="btn btn-info btn-sm">
                                <i class="fa fa-edit"></i> <?php echo e(trans_choice('core::general.edit',1)); ?>

                            </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('loan.loans.guarantors.destroy')): ?>
                            <a href="<?php echo e(url('loan/guarantor/'.$loan_guarantor->id.'/delete')); ?>" class="btn btn-danger btn-sm confirm">
                                <i class="fa fa-trash"></i> <?php echo e(trans_choice('core::general.delete',1)); ?>

                            </a>
                        <?php endif; ?>
                        <?php if(!empty($loan_guarantor->loan)): ?>
                            <a href="<?php echo e(url('loan/'.$loan_guarantor->loan_id.'/show')); ?>" class="btn btn-default btn-sm">
                                <i class="fa fa-arrow-left"></i> Back to <?php echo e(trans_choice('loan::general.loan',1)); ?>

                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">

            <!-- Profile Image -->
            <div class="box box-primary">
                <div class="box-body box-profile">
                    <?php if(!empty($loan_guarantor->photo)): ?>
                        <a href="<?php echo e(asset('storage/uploads/loans/'.$loan_guarantor->photo)); ?>"
                           class="fancybox">
                            <img
                                    class="profile-user-img img-fluid img-circle guarantor-profile-img"
                                    src="<?php echo e(asset('storage/uploads/loans/'.$loan_guarantor->photo)); ?>"
                                    alt="User profile picture">
                        </a>
                    <?php else: ?>
                        <img class="profile-user-img img-responsive img-circle guarantor-profile-img"
                             src="<?php echo e(asset('assets/dist/img/user.png')); ?>"
                             alt="User profile picture">
                    <?php endif; ?>
                    <h3 class="profile-username text-center">
                        <?php if(!empty($loan_guarantor->title)): ?>
                            <?php echo e($loan_guarantor->title->name); ?>

                        <?php endif; ?>
                        <?php echo e($loan_guarantor->first_name); ?> <?php echo e($loan_guarantor->last_name); ?></h3>

                    <p class="text-muted text-center">
                        <?php if(!empty($loan_guarantor->profession->name)): ?>
                            <?php echo e($loan_guarantor->profession->name); ?>

                        <?php endif; ?>
                    </p>

                    <div class="text-center guarantor-status">
                        <span class="label label-<?php echo e($loan_guarantor->status == 'active' ? 'success' : ($loan_guarantor->status == 'pending' ? 'warning' : 'default')); ?>">
                            <?php echo e(ucfirst($loan_guarantor->status)); ?> <?php echo e(trans_choice('loan::general.guarantor',1)); ?>

                        </span>
                        <?php if(!empty($loan_guarantor->guaranteed_amount)): ?>
                            <br><small class="text-muted">
                                Amount Guaranteed:
                                <strong><?php echo e(number_format($loan_guarantor->guaranteed_amount, 2)); ?></strong>
                            </small>
                        <?php endif; ?>
                    </div>

                    <ul class="list-group list-group-unbordered">
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('client::general.relationship',1)); ?></b>
                            <a class="pull-right">
                                <?php if(!empty($loan_guarantor->client_relationship)): ?>
                                    <?php echo e($loan_guarantor->client_relationship->name); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.mobile',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->mobile); ?>

                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.email',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->email); ?>

                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.dob',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->dob); ?>

                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.gender',1)); ?></b>
                            <a class="pull-right">
                                <?php if($loan_guarantor->gender=='male'): ?>
                                    <?php echo e(trans_choice('core::general.male',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->gender=='female'): ?>
                                    <?php echo e(trans_choice('core::general.female',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->gender=='unspecified'): ?>
                                    <?php echo e(trans_choice('core::general.unspecified',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->gender=='other'): ?>
                                    <?php echo e(trans_choice('core::general.other',1)); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('client::general.marital_status',1)); ?></b>
                            <a class="pull-right">
                                <?php if($loan_guarantor->marital_status=='single'): ?>
                                    <?php echo e(trans_choice('client::general.single',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='married'): ?>
                                    <?php echo e(trans_choice('client::general.married',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='divorced'): ?>
                                    <?php echo e(trans_choice('client::general.divorced',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='widowed'): ?>
                                    <?php echo e(trans_choice('client::general.widowed',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='other'): ?>
                                    <?php echo e(trans_choice('client::general.other',1)); ?>

                                <?php endif; ?>
                                <?php if($loan_guarantor->marital_status=='unspecified'): ?>
                                    <?php echo e(trans_choice('core::general.unspecified',1)); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.zip',1)); ?></b>
                            <a class="pull-right">
                                <?php echo e($loan_guarantor->zip); ?>

                            </a>
                        </li>
                        <li class="list-group-item">
                            <b><?php echo e(trans_choice('core::general.status',1)); ?></b>
                            <a class="pull-right">
                                <span class="label label-<?php echo e($loan_guarantor->status == 'active' ? 'success' : ($loan_guarantor->status == 'pending' ? 'warning' : 'default')); ?>">
                                    <?php echo e(ucfirst($loan_guarantor->status)); ?>

                                </span>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>Guaranteed Amount</b>
                            <a class="pull-right">
                                <?php if(!empty($loan_guarantor->guaranteed_amount)): ?>
                                    <?php echo e(number_format($loan_guarantor->guaranteed_amount, 2)); ?>

                                <?php else: ?>
                                    <?php echo e(trans_choice('core::general.not_specified',1)); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->

            <!-- About Me Box -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><?php echo e(trans_choice('core::general.extra',1)); ?> <?php echo e(trans_choice('core::general.detail',2)); ?></h3>
                </div>
                <!-- /.box-header -->
                <div class="box-body">

                    <strong><i class="fa fa-map-marker margin-r-5"></i> <?php echo e(trans_choice('core::general.address',1)); ?>

                    </strong>

                    <p class="text-muted">
                        <?php echo e($loan_guarantor->address); ?><br>
                        <?php if(!empty($loan_guarantor->country)): ?>
                            <?php echo e($loan_guarantor->country->name); ?>

                        <?php endif; ?>
                    </p>
                    <hr>

                    <strong><i class="fa fa-file-text-o margin-r-5"></i> <?php echo e(trans_choice('core::general.note',2)); ?>

                    </strong>

                    <p> <?php echo e($loan_guarantor->notes); ?></p>

                    <?php if($custom_fields->count() > 0): ?>
                        <hr>
                        <strong><i class="fa fa-list margin-r-5"></i> <?php echo e(trans_choice('customfield::general.custom_field',2)); ?>

                        </strong>
                        <div class="row">
                            <?php $__currentLoopData = $custom_fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $custom_field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                $custom_field_meta = \Modules\CustomField\Entities\CustomFieldMeta::where('category', 'add_guarantor')
                                    ->where('parent_id', $loan_guarantor->id)
                                    ->where('custom_field_id', $custom_field->id)
                                    ->first();
                                ?>
                                <?php if(!empty($custom_field_meta)): ?>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label><?php echo e($custom_field->name); ?>:</label>
                                            <div class="custom-field-value">
                                                <?php if($custom_field->type == 'file' && !empty($custom_field_meta->value)): ?>
                                                    <a href="<?php echo e(asset('storage/uploads/custom_fields/'.$custom_field_meta->value)); ?>"
                                                       target="_blank" class="btn btn-sm btn-default">
                                                        <i class="fa fa-download"></i> <?php echo e($custom_field_meta->value); ?>

                                                    </a>
                                                <?php elseif($custom_field->type == 'date' && !empty($custom_field_meta->value)): ?>
                                                    <span class="text-muted">
                                                        <i class="fa fa-calendar"></i> <?php echo e(date('Y-m-d', strtotime($custom_field_meta->value))); ?>

                                                    </span>
                                                <?php elseif($custom_field->type == 'checkbox'): ?>
                                                    <span class="label label-<?php echo e($custom_field_meta->value ? 'success' : 'default'); ?>">
                                                        <?php echo e($custom_field_meta->value ? trans_choice('core::general.yes',1) : trans_choice('core::general.no',1)); ?>

                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-info"><?php echo e($custom_field_meta->value); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->
        </div>
        <!-- /.col -->

        <div class="col-md-9">
            <!-- Loan Information Box -->
            <?php if(!empty($loan_guarantor->loan)): ?>
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-money"></i> <?php echo e(trans_choice('loan::general.loan',1)); ?> Information
                    </h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong><?php echo e(trans_choice('loan::general.loan',1)); ?> <?php echo e(trans_choice('core::general.id',1)); ?>:</strong></td>
                                    <td>
                                        <a href="<?php echo e(url('loan/'.$loan_guarantor->loan->id.'/show')); ?>">
                                            #<?php echo e($loan_guarantor->loan->id); ?>

                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(trans_choice('loan::general.product',1)); ?>:</strong></td>
                                    <td>
                                        <?php if(!empty($loan_guarantor->loan->loan_product)): ?>
                                            <?php echo e($loan_guarantor->loan->loan_product->name); ?>

                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(trans_choice('core::general.amount',1)); ?>:</strong></td>
                                    <td><?php echo e(number_format($loan_guarantor->loan->principal, 2)); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(trans_choice('core::general.status',1)); ?>:</strong></td>
                                    <td>
                                        <span class="label label-<?php echo e($loan_guarantor->loan->status == 'active' ? 'success' : ($loan_guarantor->loan->status == 'pending' ? 'warning' : 'default')); ?>">
                                            <?php echo e(ucfirst($loan_guarantor->loan->status)); ?>

                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Client Name</strong></td>
                                    <td>
                                        <?php if(!empty($loan_guarantor->loan->client)): ?>
                                            <a href="<?php echo e(url('client/'.$loan_guarantor->loan->client->id.'/show')); ?>">
                                                <?php echo e($loan_guarantor->loan->client->first_name); ?> <?php echo e($loan_guarantor->loan->client->last_name); ?>

                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Disbursedd Date</strong></td>
                                    <td><?php echo e($loan_guarantor->loan->disbursed_on_date); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Maturity Date</strong></td>
                                    <td><?php echo e($loan_guarantor->loan->expected_maturity_date); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Interest Rate</strong></td>
                                    <td><?php echo e($loan_guarantor->loan->interest_rate); ?>%</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Additional Information Box -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-info-circle"></i> Additional Details
                    </h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Loan Officer:</strong></td>
                                    <td><?php echo e($loan_guarantor->employer); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Phone No.</strong></td>
                                    <td><?php echo e($loan_guarantor->phone); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>City:</strong></td>
                                    <td><?php echo e($loan_guarantor->city); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Region:</strong></td>
                                    <td><?php echo e($loan_guarantor->state); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Created Date:</strong></td>
                                    <td><?php echo e($loan_guarantor->created_date); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Joined Date:</strong></td>
                                    <td><?php echo e($loan_guarantor->joined_date); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(trans_choice('core::general.created_by',1)); ?>:</strong></td>
                                    <td>
                                        <?php if(!empty($loan_guarantor->created_by)): ?>
                                            <?php echo e($loan_guarantor->created_by->first_name); ?> <?php echo e($loan_guarantor->created_by->last_name); ?>

                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo e(trans_choice('core::general.created_at',1)); ?>:</strong></td>
                                    <td><?php echo e($loan_guarantor->created_at); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.col -->
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script>
        $('.data-table').DataTable({
            "language": {
                "lengthMenu": "<?php echo e(trans('general.lengthMenu')); ?>",
                "zeroRecords": "<?php echo e(trans('general.zeroRecords')); ?>",
                "info": "<?php echo e(trans('general.info')); ?>",
                "infoEmpty": "<?php echo e(trans('general.infoEmpty')); ?>",
                "search": "<?php echo e(trans('general.search')); ?>",
                "infoFiltered": "<?php echo e(trans('general.infoFiltered')); ?>",
                "paginate": {
                    "first": "<?php echo e(trans('general.first')); ?>",
                    "last": "<?php echo e(trans('general.last')); ?>",
                    "next": "<?php echo e(trans('general.next')); ?>",
                    "previous": "<?php echo e(trans('general.previous')); ?>"
                },
                "columnDefs": [
                    {"orderable": false, "targets": 0}
                ]
            },
            responsive: true
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('core::layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\annex\Modules\Loan\Providers/../Resources/views/themes/adminlte/guarantor/show.blade.php ENDPATH**/ ?>