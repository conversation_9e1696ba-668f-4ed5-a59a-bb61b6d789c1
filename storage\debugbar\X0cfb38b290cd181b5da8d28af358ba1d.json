{"__meta": {"id": "X0cfb38b290cd181b5da8d28af358ba1d", "datetime": "2025-08-08 18:30:19", "utime": **********.395325, "method": "POST", "uri": "/annex/public/loan/store", "ip": "**************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754670618.325759, "end": **********.39536, "duration": 1.069601058959961, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1754670618.325759, "relative_start": 0, "end": **********.143254, "relative_end": **********.143254, "duration": 0.8174951076507568, "duration_str": "817ms", "params": [], "collector": null}, {"label": "Application", "start": **********.145201, "relative_start": 0.8194420337677002, "end": **********.395366, "relative_end": 5.9604644775390625e-06, "duration": 0.2501649856567383, "duration_str": "250ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 47433712, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST loan/store", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanController@store", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:503-588"}, "queries": {"nb_statements": 21, "nb_failed_statements": 0, "accumulated_duration": 0.053599999999999995, "accumulated_duration_str": "53.6ms", "statements": [{"sql": "select * from `users` where `id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.01282, "duration_str": "12.82ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 10 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["10", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.00556, "duration_str": "5.56ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 10 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["10", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select * from `loan_products` where `loan_products`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 520}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `clients` where `clients`.`id` = '129' limit 1", "type": "query", "params": [], "bindings": ["129"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 521}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "insert into `loans` (`currency_id`, `loan_product_id`, `client_id`, `branch_id`, `loan_transaction_processing_strategy_id`, `loan_purpose_id`, `loan_officer_id`, `expected_disbursement_date`, `expected_first_payment_date`, `fund_id`, `created_by_id`, `applied_amount`, `loan_term`, `repayment_frequency`, `repayment_frequency_type`, `interest_rate`, `interest_rate_type`, `grace_on_principal_paid`, `grace_on_interest_paid`, `grace_on_interest_charged`, `interest_methodology`, `amortization_method`, `auto_disburse`, `submitted_on_date`, `submitted_by_user_id`, `updated_at`, `created_at`) values (1, 3, 129, 2, 1, '3', '10', '2025-08-08', '2025-09-08', '1', 10, '1500.000000', '4', '1', 'months', '5.500000', 'month', 0, 0, 0, 'flat', 'equal_installments', 0, '2025-08-08', 10, '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["1", "3", "129", "2", "1", "3", "10", "2025-08-08", "2025-09-08", "1", "10", "1500.000000", "4", "1", "months", "5.500000", "month", "0", "0", "0", "flat", "equal_installments", "0", "2025-08-08", "10", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 548}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0028799999999999997, "duration_str": "2.88ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:548", "connection": "annex"}, {"sql": "select * from `loan_charges` where `loan_charges`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 552}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "insert into `loan_linked_charges` (`loan_id`, `name`, `loan_charge_id`, `amount`, `loan_charge_type_id`, `loan_charge_option_id`, `is_penalty`, `updated_at`, `created_at`) values (811, 'Monthly Loan Processing Fee', 3, '4.000000', 2, 7, 0, '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["811", "Monthly Loan Processing Fee", "3", "4.000000", "2", "7", "0", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 565}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:565", "connection": "annex"}, {"sql": "select * from `loan_charges` where `loan_charges`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 552}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "insert into `loan_linked_charges` (`loan_id`, `name`, `loan_charge_id`, `amount`, `loan_charge_type_id`, `loan_charge_option_id`, `is_penalty`, `updated_at`, `created_at`) values (811, 'Late Payment Fee', 6, '20.000000', 2, 1, 0, '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["811", "Late Payment Fee", "6", "20.000000", "2", "1", "0", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 565}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:565", "connection": "annex"}, {"sql": "insert into `loan_history` (`loan_id`, `created_by_id`, `user`, `action`, `updated_at`, `created_at`) values (811, 10, '<PERSON><PERSON>', '<PERSON><PERSON> Created', '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["811", "10", "<PERSON><PERSON>", "<PERSON>an <PERSON>", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 573}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0017, "duration_str": "1.7ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:573", "connection": "annex"}, {"sql": "insert into `loan_officer_history` (`loan_id`, `created_by_id`, `loan_officer_id`, `start_date`, `updated_at`, `created_at`) values (811, 10, '10', '2025-08-08', '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["811", "10", "10", "2025-08-08", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 579}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00177, "duration_str": "1.77ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:579", "connection": "annex"}, {"sql": "select * from `custom_fields` where `category` = 'add_loan' and `active` = 1", "type": "query", "params": [], "bindings": ["add_loan", "1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\Modules\\CustomField\\Helpers\\general_helper.php", "line": 140}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\Modules\\CustomField\\Helpers\\general_helper.php:140", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where (`category` = 'add_loan' and `parent_id` = 811 and `custom_field_id` = 8) limit 1", "type": "query", "params": [], "bindings": ["add_loan", "811", "8"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 20, "namespace": null, "name": "\\Modules\\CustomField\\Helpers\\general_helper.php", "line": 183}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00624, "duration_str": "6.24ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "insert into `custom_fields_meta` (`category`, `parent_id`, `custom_field_id`, `value`, `updated_at`, `created_at`) values ('add_loan', 811, 8, '1869', '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["add_loan", "811", "8", "1869", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php", "line": 263}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 21, "namespace": null, "name": "\\Modules\\CustomField\\Helpers\\general_helper.php", "line": 183}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}], "duration": 0.00172, "duration_str": "1.72ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:263", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where (`category` = 'add_loan' and `parent_id` = 811 and `custom_field_id` = 15) limit 1", "type": "query", "params": [], "bindings": ["add_loan", "811", "15"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 20, "namespace": null, "name": "\\Modules\\CustomField\\Helpers\\general_helper.php", "line": 183}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00561, "duration_str": "5.61ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "insert into `custom_fields_meta` (`category`, `parent_id`, `custom_field_id`, `value`, `updated_at`, `created_at`) values ('add_loan', 811, 15, '1500 PERSONAL LOAN', '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["add_loan", "811", "15", "1500 PERSONAL LOAN", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php", "line": 263}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 21, "namespace": null, "name": "\\Modules\\CustomField\\Helpers\\general_helper.php", "line": 183}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:263", "connection": "annex"}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `causer_id`, `causer_type`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('default', '{\\\"id\\\":811}', 10, '<PERSON><PERSON><PERSON>\\User\\Entities\\User', 811, 'Mo<PERSON><PERSON>\\Loan\\Entities\\Loan', 'Create Loan', '2025-08-08 18:30:19', '2025-08-08 18:30:19')", "type": "query", "params": [], "bindings": ["default", "{&quot;id&quot;:811}", "10", "Modules\\User\\Entities\\User", "811", "Modules\\Loan\\Entities\\Loan", "Create Loan", "2025-08-08 18:30:19", "2025-08-08 18:30:19"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 161}, {"index": 16, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 583}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0033399999999999997, "duration_str": "3.34ms", "stmt_id": "\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php:161", "connection": "annex"}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', **********, **********, '{\\\"uuid\\\":\\\"fdec9d1f-871a-4b94-9b02-ec6ec1d155e3\\\",\\\"displayName\\\":\\\"Modules\\\\Loan\\\\Listeners\\\\LoanStatusChangedCampaigns\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\Events\\\\CallQueuedListener\\\",\\\"command\\\":\\\"O:36:\\\\\"Illuminate\\\\Events\\\\CallQueuedListener\\\\\":16:{s:5:\\\\\"class\\\\\";s:49:\\\\\"Modules\\\\Loan\\\\Listeners\\\\LoanStatusChangedCampaigns\\\\\";s:6:\\\\\"method\\\\\";s:6:\\\\\"handle\\\\\";s:4:\\\\\"data\\\\\";a:1:{i:0;O:37:\\\\\"Modules\\\\Loan\\\\Events\\\\LoanStatusChanged\\\\\":2:{s:4:\\\\\"loan\\\\\";O:45:\\\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\\\":4:{s:5:\\\\\"class\\\\\";s:26:\\\\\"Modules\\\\Loan\\\\Entities\\\\Loan\\\\\";s:2:\\\\\"id\\\\\";i:811;s:9:\\\\\"relations\\\\\";a:0:{}s:10:\\\\\"connection\\\\\";s:5:\\\\\"mysql\\\\\";}s:15:\\\\\"previous_status\\\\\";s:0:\\\\\"\\\\\";}}s:5:\\\\\"tries\\\\\";N;s:7:\\\\\"backoff\\\\\";N;s:10:\\\\\"retryUntil\\\\\";N;s:7:\\\\\"timeout\\\\\";N;s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "**********", "**********", "{&quot;uuid&quot;:&quot;fdec9d1f-871a-4b94-9b02-ec6ec1d155e3&quot;,&quot;displayName&quot;:&quot;Modules\\\\Loan\\\\Listeners\\\\LoanStatusChangedCampaigns&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;Illuminate\\\\Events\\\\CallQueuedListener&quot;,&quot;command&quot;:&quot;O:36:\\&quot;Illuminate\\\\Events\\\\CallQueuedListener\\&quot;:16:{s:5:\\&quot;class\\&quot;;s:49:\\&quot;Modules\\\\Loan\\\\Listeners\\\\LoanStatusChangedCampaigns\\&quot;;s:6:\\&quot;method\\&quot;;s:6:\\&quot;handle\\&quot;;s:4:\\&quot;data\\&quot;;a:1:{i:0;O:37:\\&quot;Modules\\\\Loan\\\\Events\\\\LoanStatusChanged\\&quot;:2:{s:4:\\&quot;loan\\&quot;;O:45:\\&quot;Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\&quot;:4:{s:5:\\&quot;class\\&quot;;s:26:\\&quot;Modules\\\\Loan\\\\Entities\\\\Loan\\&quot;;s:2:\\&quot;id\\&quot;;i:811;s:9:\\&quot;relations\\&quot;;a:0:{}s:10:\\&quot;connection\\&quot;;s:5:\\&quot;mysql\\&quot;;}s:15:\\&quot;previous_status\\&quot;;s:0:\\&quot;\\&quot;;}}s:5:\\&quot;tries\\&quot;;N;s:7:\\&quot;backoff\\&quot;;N;s:10:\\&quot;retryUntil\\&quot;;N;s:7:\\&quot;timeout\\&quot;;N;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": [], "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 163}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 84}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0018700000000000001, "duration_str": "1.87ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:163", "connection": "annex"}]}, "models": {"data": {"Modules\\CustomField\\Entities\\CustomField": 2, "Modules\\Loan\\Entities\\LoanCharge": 2, "Modules\\Client\\Entities\\Client": 1, "Modules\\Loan\\Entities\\LoanProduct": 1, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 1}, "count": 8}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<PERSON><PERSON><PERSON>@mjy3.com\"\n  \"user\" => array:24 [\n    \"id\" => 10\n    \"created_by_id\" => null\n    \"branch_id\" => null\n    \"name\" => \"\"\n    \"username\" => null\n    \"email\" => \"<PERSON>a<PERSON>@mjy3.com\"\n    \"email_verified_at\" => \"2025-01-16T03:14:17.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"<PERSON><PERSON>\"\n    \"last_name\" => \"Eshun\"\n    \"phone\" => \"0539862144\"\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"female\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => \"All information on this data base is strictly confidential and for internal use only.\"\n    \"photo\" => \"T7Bw0DyVu5aY3P7deOzEBPE24SSqQaob2r9gRRPQ.jpeg\"\n    \"created_at\" => \"2025-01-16T03:14:17.000000Z\"\n    \"updated_at\" => \"2025-01-16T23:28:11.000000Z\"\n    \"full_name\" => \"<PERSON><PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 4\n        \"is_system\" => 0\n        \"name\" => \"Loan Officer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"updated_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 10\n          \"role_id\" => 4\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n  ]\n]", "api": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 1, "messages": [{"message": "[ability => loan.loans.create, result => true, user => 10, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1570635026 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570635026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.286933}]}, "session": {"_token": "OAYgquvl16WZFn5bSfhFjghMpIbfSyamH58qA6TF", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://*************/annex/public/loan/create\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"flash_notification\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "10", "flash_notification": "Illuminate\\Support\\Collection {#2156\n  #items: array:1 [\n    0 => Laracasts\\Flash\\Message {#2155\n      +title: null\n      +message: \"Successfully Saved\"\n      +level: \"success\"\n      +important: true\n      +overlay: false\n    }\n  ]\n}", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://*************/annex/public/_debugbar/telescope/9f961be5-9052-4b74-9306-0ebad54047dc\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan/store", "status_code": "<pre class=sf-dump id=sf-dump-401265744 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-401265744\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1435451111 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1435451111\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2022376287 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OAYgquvl16WZFn5bSfhFjghMpIbfSyamH58qA6TF</span>\"\n  \"<span class=sf-dump-key>client_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">client</span>\"\n  \"<span class=sf-dump-key>client_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">129</span>\"\n  \"<span class=sf-dump-key>loan_product_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>applied_amount</span>\" => \"<span class=sf-dump-str title=\"11 characters\">1500.000000</span>\"\n  \"<span class=sf-dump-key>fund_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>loan_term</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>repayment_frequency</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>repayment_frequency_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">months</span>\"\n  \"<span class=sf-dump-key>interest_rate</span>\" => \"<span class=sf-dump-str title=\"8 characters\">5.500000</span>\"\n  \"<span class=sf-dump-key>expected_disbursement_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-08</span>\"\n  \"<span class=sf-dump-key>field_8</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1869</span>\"\n  \"<span class=sf-dump-key>field_15</span>\" => \"<span class=sf-dump-str title=\"18 characters\">1500 PERSONAL LOAN</span>\"\n  \"<span class=sf-dump-key>loan_officer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>loan_purpose_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>expected_first_payment_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-08</span>\"\n  \"<span class=sf-dump-key>charges</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-key>3</span> => \"<span class=sf-dump-str title=\"8 characters\">4.000000</span>\"\n    <span class=sf-dump-key>6</span> => \"<span class=sf-dump-str title=\"9 characters\">20.000000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>loan_charges</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022376287\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-250933690 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">442</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://*************/annex/public/loan/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IjN3VDhWNWpuS3BiTXFrZ3RRam4vUVE9PSIsInZhbHVlIjoiOU0raCtnMlVqckpiNVEwME84WkZUYVQ2MGczZkdIOGtmSWVtOVZGWG5qMnBUaGtEVTZOd0Q1UC9USlNkSC91eFFlb0txbmJzUE5ZL2R3Qk1SV0UrV0xCL0trR3ZRRTJFYkQrdGVQNXQzMGNCTUF0VVNZSmc1L2NnL1pjWUNXc1ciLCJtYWMiOiJmNTk3ZWFkNDQwNzYwMGEyYzlhNDU4Mzc3MGZhYzUxMmNhYWIyMmI2NWNiOGNiYTUyYmQ0MGM2MzY5ZGFlY2Y0In0%3D; mjy3_micro_loans_session=eyJpdiI6IlczZjRWeDBWK3gxRU01Z2RGcmVGZXc9PSIsInZhbHVlIjoiWHZBZlpvR0VxSWE1QVphVUExS2xnWTQ1c2hTcHMyY2ZaeS9la1ZQQ1BtUXFwbHBvaVloSmc2ZllOdnkvcmRobmVJYmJFRU5NcDJkelkyUUVUV0xvMjBCT0U5MDU1Ry9IME5NYzkwcHlsWUpwWk11N0ZiOHBxeWp5Zyt1TDJDKzgiLCJtYWMiOiJhY2Q2MjIxOGQ1ZWJiMDI0OTdiMWNiOTBhODA3YjQ0NzZjYjZkMWJmZDNkMDk5ZmVhMDM0ZGUxYTY2YTcxMjdlIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250933690\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1274753109 data-indent-pad=\"  \"><span class=sf-dump-note>array:54</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">442</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://*************</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://*************/annex/public/loan/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IjN3VDhWNWpuS3BiTXFrZ3RRam4vUVE9PSIsInZhbHVlIjoiOU0raCtnMlVqckpiNVEwME84WkZUYVQ2MGczZkdIOGtmSWVtOVZGWG5qMnBUaGtEVTZOd0Q1UC9USlNkSC91eFFlb0txbmJzUE5ZL2R3Qk1SV0UrV0xCL0trR3ZRRTJFYkQrdGVQNXQzMGNCTUF0VVNZSmc1L2NnL1pjWUNXc1ciLCJtYWMiOiJmNTk3ZWFkNDQwNzYwMGEyYzlhNDU4Mzc3MGZhYzUxMmNhYWIyMmI2NWNiOGNiYTUyYmQ0MGM2MzY5ZGFlY2Y0In0%3D; mjy3_micro_loans_session=eyJpdiI6IlczZjRWeDBWK3gxRU01Z2RGcmVGZXc9PSIsInZhbHVlIjoiWHZBZlpvR0VxSWE1QVphVUExS2xnWTQ1c2hTcHMyY2ZaeS9la1ZQQ1BtUXFwbHBvaVloSmc2ZllOdnkvcmRobmVJYmJFRU5NcDJkelkyUUVUV0xvMjBCT0U5MDU1Ry9IME5NYzkwcHlsWUpwWk11N0ZiOHBxeWp5Zyt1TDJDKzgiLCJtYWMiOiJhY2Q2MjIxOGQ1ZWJiMDI0OTdiMWNiOTBhODA3YjQ0NzZjYjZkMWJmZDNkMDk5ZmVhMDM0ZGUxYTY2YTcxMjdlIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"99 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at ************* Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"14 characters\">**************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">48656</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/annex/public/loan/store</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/annex/public/loan/store</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754670618.3258</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754670618</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274753109\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1911143332 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OAYgquvl16WZFn5bSfhFjghMpIbfSyamH58qA6TF</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nN11lQvsyoUgXImmoJdavcedd7Gu6PSjd9QBx8bH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911143332\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1933726799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 16:30:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://*************/annex/public/loan/811/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6InNOZFZ1akFoK2VIakIrbms2ZG55SVE9PSIsInZhbHVlIjoiUG5kSkUya3JuZTdYV1ovdFBHMVM5R0VDR0piTW1WSWhzdDJzdVY4QUZ2NFNzZXlTVzljNzNYWkxpdzNMSFQ3a1JiY0ZYSjR1Rk9vRGFSRTQ1OXM4Tkw2L1c3VTlqYnhzWm1UVG1BVGU0MklhSnJJdlJaOUo0TklUWXVmOUh5L0EiLCJtYWMiOiI5ZjY2N2Q2ZGM1MGM3Y2M1ZTYxNDFmMzgzNDY0MTEwMGIzZmZkMWNmYjBkNTE4OGZlOTY4Mjc5MTVkMjlkYTU1In0%3D; expires=Sat, 09-Aug-2025 12:30:19 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6InlMUVFEaHdXTzRZbjBPaWVRNHphZ1E9PSIsInZhbHVlIjoiTUMvb0V6Z1JnQ0RCMVBXQ3YvZmIzUDZvclJkMUpiRjhyZWJxazl0bTdrZ0pDY0FNZWtkL2R1cWc2WGZnRXRQc3RLVi8yTGJ0bGtjMWdueER5UitMRlpTNnV5K1FJYmRJaEFMTVRyU0wzdkcxS3QwZHEySHRzMmVLSzZES21jTmciLCJtYWMiOiI0ZjE5YmZlMDhiYTdjYzEyMWY0YzU4NTQzOTU4MGIwODhjNTA3NTZiYzk0MzEzMTc0OGI5NThjNzEwMDRhZjZmIn0%3D; expires=Sat, 09-Aug-2025 12:30:19 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6InNOZFZ1akFoK2VIakIrbms2ZG55SVE9PSIsInZhbHVlIjoiUG5kSkUya3JuZTdYV1ovdFBHMVM5R0VDR0piTW1WSWhzdDJzdVY4QUZ2NFNzZXlTVzljNzNYWkxpdzNMSFQ3a1JiY0ZYSjR1Rk9vRGFSRTQ1OXM4Tkw2L1c3VTlqYnhzWm1UVG1BVGU0MklhSnJJdlJaOUo0TklUWXVmOUh5L0EiLCJtYWMiOiI5ZjY2N2Q2ZGM1MGM3Y2M1ZTYxNDFmMzgzNDY0MTEwMGIzZmZkMWNmYjBkNTE4OGZlOTY4Mjc5MTVkMjlkYTU1In0%3D; expires=Sat, 09-Aug-2025 12:30:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6InlMUVFEaHdXTzRZbjBPaWVRNHphZ1E9PSIsInZhbHVlIjoiTUMvb0V6Z1JnQ0RCMVBXQ3YvZmIzUDZvclJkMUpiRjhyZWJxazl0bTdrZ0pDY0FNZWtkL2R1cWc2WGZnRXRQc3RLVi8yTGJ0bGtjMWdueER5UitMRlpTNnV5K1FJYmRJaEFMTVRyU0wzdkcxS3QwZHEySHRzMmVLSzZES21jTmciLCJtYWMiOiI0ZjE5YmZlMDhiYTdjYzEyMWY0YzU4NTQzOTU4MGIwODhjNTA3NTZiYzk0MzEzMTc0OGI5NThjNzEwMDRhZjZmIn0%3D; expires=Sat, 09-Aug-2025 12:30:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933726799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1435235973 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OAYgquvl16WZFn5bSfhFjghMpIbfSyamH58qA6TF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://*************/annex/public/loan/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">flash_notification</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>10</span>\n  \"<span class=sf-dump-key>flash_notification</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2156</a><samp>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Laracasts\\Flash\\Message\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Laracasts\\Flash</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Message</span> {<a class=sf-dump-ref>#2155</a><samp>\n        +<span class=sf-dump-public title=\"Public property\">title</span>: <span class=sf-dump-const>null</span>\n        +<span class=sf-dump-public title=\"Public property\">message</span>: \"<span class=sf-dump-str title=\"18 characters\">Successfully Saved</span>\"\n        +<span class=sf-dump-public title=\"Public property\">level</span>: \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        +<span class=sf-dump-public title=\"Public property\">important</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">overlay</span>: <span class=sf-dump-const>false</span>\n      </samp>}\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435235973\", {\"maxDepth\":0})</script>\n"}}