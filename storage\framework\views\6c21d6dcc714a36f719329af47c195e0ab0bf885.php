<?php $__env->startSection('title'); ?>
    <?php echo e(trans_choice('core::general.edit',1)); ?> <?php echo e(trans_choice('loan::general.guarantor',1)); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <?php echo e(trans_choice('core::general.edit',1)); ?> <?php echo e(trans_choice('loan::general.guarantor',1)); ?>

                        <a href="#" onclick="window.history.back()"
                           class="btn btn-outline-light bg-white d-none d-sm-inline-flex">
                            <em class="icon ni ni-arrow-left"></em><span><?php echo e(trans_choice('core::general.back',1)); ?></span>
                        </a>
                    </h1>

                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a
                                    href="<?php echo e(url('dashboard')); ?>"><?php echo e(trans_choice('dashboard::general.dashboard',1)); ?></a>
                        </li>
                        <li class="breadcrumb-item"><a
                                    href="<?php echo e(url('loan/'.$loan_guarantor->loan_id.'/show')); ?>"><?php echo e(trans_choice('loan::general.loan',2)); ?></a>
                        </li>
                        <li class="breadcrumb-item active"><?php echo e(trans_choice('core::general.edit',1)); ?> <?php echo e(trans_choice('loan::general.guarantor',1)); ?></li>
                    </ol>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    <section class="content" id="app">
        <form method="post" action="<?php echo e(url('loan/guarantor/'.$loan_guarantor->id.'/update')); ?>" enctype="multipart/form-data">
            <?php echo e(csrf_field()); ?>

            <div class="card card-bordered card-preview">
                <div class="card-body">
                    <div class="form-group">
                        <label for="is_client"
                               class="control-label"><?php echo e(trans_choice('loan::general.is_client',1)); ?></label>
                        <select class="form-control" name="is_client" id="is_client" v-model="is_client" required>
                            <option value="1" selected><?php echo e(trans_choice('core::general.yes',1)); ?></option>
                            <option value="0"><?php echo e(trans_choice('core::general.no',1)); ?></option>
                        </select>
                    </div>
                    <div v-if="is_client=='1'">
                        <div class="form-group">
                            <label for="client_id"
                                   class="control-label"><?php echo e(trans_choice('client::general.client',1)); ?></label>
                            <v-select label="name_id" :options="clients"
                                      :reduce="client => client.id"
                                      v-model="client_id">
                                <template #search="{attributes, events}">
                                    <input
                                            autocomplete="off"
                                            class="vs__search <?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            v-bind="attributes"
                                            :required="!client_id"
                                            v-on="events"
                                    />
                                </template>
                            </v-select>
                            <input type="hidden" name="client_id"
                                   v-model="client_id">
                            <?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div v-if="is_client=='0'">
                        <div class="form-group">
                            <label for="client_relationship_id"
                                   class="control-label"><?php echo e(trans_choice('client::general.relationship',1)); ?></label>
                            <v-select label="name" :options="client_relationships"
                                      :reduce="client_relationship => client_relationship.id"
                                      v-model="client_relationship_id">
                                <template #search="{attributes, events}">
                                    <input
                                            autocomplete="off"
                                            class="vs__search <?php $__errorArgs = ['client_relationship_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            v-bind="attributes"
                                            :required="!client_relationship_id"
                                            v-on="events"
                                    />
                                </template>
                            </v-select>
                            <input type="hidden" name="client_relationship_id"
                                   v-model="client_relationship_id">
                            <?php $__errorArgs = ['client_relationship_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="title_id"
                                   class="control-label"><?php echo e(trans_choice('client::general.title',1)); ?></label>
                            <v-select label="name" :options="titles"
                                      :reduce="title => title.id"
                                      v-model="title_id">
                                <template #search="{attributes, events}">
                                    <input
                                            autocomplete="off"
                                            class="vs__search <?php $__errorArgs = ['title_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            v-bind="attributes"
                                            v-on="events"
                                    />
                                </template>
                            </v-select>
                            <input type="hidden" name="title_id"
                                   v-model="title_id">
                            <?php $__errorArgs = ['title_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="first_name"
                                   class="control-label"><?php echo e(trans_choice('core::general.first_name',1)); ?></label>
                            <input type="text" name="first_name" id="first_name" v-model="first_name"
                                   class="form-control <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                            <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="last_name"
                                   class="control-label"><?php echo e(trans_choice('core::general.last_name',1)); ?></label>
                            <input type="text" name="last_name" id="last_name" v-model="last_name"
                                   class="form-control <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                            <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="gender"
                                   class="control-label"><?php echo e(trans_choice('core::general.gender',1)); ?></label>
                            <select class="form-control <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="gender"
                                    id="gender" v-model="gender" required>
                                <option value="male"><?php echo e(trans_choice("core::general.male",1)); ?></option>
                                <option value="female"><?php echo e(trans_choice("core::general.female",1)); ?></option>
                            </select>
                            <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="marital_status"
                                   class="control-label"><?php echo e(trans_choice('client::general.marital_status',1)); ?></label>
                            <select class="form-control <?php $__errorArgs = ['marital_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    name="marital_status"
                                    id="marital_status" v-model="marital_status">
                                <option value=""></option>
                                <option value="single"><?php echo e(trans_choice("client::general.single",1)); ?></option>
                                <option value="married"><?php echo e(trans_choice("client::general.married",1)); ?></option>
                                <option value="divorced"><?php echo e(trans_choice("client::general.divorced",1)); ?></option>
                                <option value="widowed"><?php echo e(trans_choice("client::general.widowed",1)); ?></option>
                            </select>
                            <?php $__errorArgs = ['marital_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="mobile"
                                   class="control-label"><?php echo e(trans_choice('core::general.mobile',1)); ?></label>
                            <input type="text" name="mobile" id="mobile" v-model="mobile"
                                   class="form-control <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="country_id"
                                   class="control-label"><?php echo e(trans_choice('core::general.country',1)); ?></label>
                            <v-select label="name" :options="countries"
                                      :reduce="country => country.id"
                                      v-model="country_id">
                                <template #search="{attributes, events}">
                                    <input
                                            autocomplete="off"
                                            class="vs__search <?php $__errorArgs = ['country_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            v-bind="attributes"
                                            v-on="events"
                                    />
                                </template>
                            </v-select>
                            <input type="hidden" name="country_id"
                                   v-model="country_id">
                            <?php $__errorArgs = ['country_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="dob"
                                   class="control-label"><?php echo e(trans_choice('core::general.dob',1)); ?></label>
                            <flat-pickr
                                    v-model="dob"
                                    class="form-control  <?php $__errorArgs = ['dob'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    name="dob" required>
                            </flat-pickr>
                            <?php $__errorArgs = ['dob'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="email"
                                   class="control-label"><?php echo e(trans_choice('core::general.email',1)); ?></label>
                            <input type="email" name="email" id="email" v-model="email"
                                   class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="profession_id"
                                   class="control-label"><?php echo e(trans_choice('client::general.profession',1)); ?></label>
                            <v-select label="name" :options="professions"
                                      :reduce="profession => profession.id"
                                      v-model="profession_id">
                                <template #search="{attributes, events}">
                                    <input
                                            autocomplete="off"
                                            class="vs__search <?php $__errorArgs = ['profession_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            v-bind="attributes"
                                            v-on="events"
                                    />
                                </template>
                            </v-select>
                            <input type="hidden" name="profession_id"
                                   v-model="profession_id">
                            <?php $__errorArgs = ['profession_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="photo"
                                   class="control-label"><?php echo e(trans_choice('core::general.photo',1)); ?></label>
                            <input type="file" name="photo" id="photo"
                                   class="form-control <?php $__errorArgs = ['photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="address"
                                   class="control-label"><?php echo e(trans_choice('core::general.address',1)); ?></label>
                            <textarea type="text" name="address" v-model="address"
                                      id="address"
                                      class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                </textarea>
                            <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>


                        <div class="form-group">
                            <label for="notes"
                                   class="control-label"><?php echo e(trans_choice('core::general.note',2)); ?></label>
                            <textarea type="text" name="notes" v-model="notes"
                                      id="notes"
                                      class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                </textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="guaranteed_amount" class="control-label"><?php echo e(trans('core::general.amount')); ?></label>
                        <input type="number" name="guaranteed_amount" value="<?php echo e(old('guaranteed_amount')); ?>"
                               id="guaranteed_amount" v-model="guaranteed_amount"
                               class="form-control <?php $__errorArgs = ['guaranteed_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> numeric">
                        <?php $__errorArgs = ['guaranteed_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="invalid-feedback" role="alert">
                                        <strong><?php echo e($message); ?></strong>
                                    </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <?php $__currentLoopData = $custom_fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $custom_field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                        $custom_field_value = '';
                        $custom_field_meta = \Modules\CustomField\Entities\CustomFieldMeta::where('category', 'add_guarantor')
                            ->where('parent_id', $loan_guarantor->id)
                            ->where('custom_field_id', $custom_field->id)
                            ->first();
                        if (!empty($custom_field_meta)) {
                            $custom_field_value = $custom_field_meta->value;
                        }
                        ?>
                        <div class="form-group">
                            <label for="custom_field_<?php echo e($custom_field->id); ?>"
                                   class="control-label"><?php echo e($custom_field->name); ?></label>
                            <?php if($custom_field->type == 'textfield'): ?>
                                <input type="text" name="custom_fields[<?php echo e($custom_field->id); ?>]"
                                       class="form-control <?php $__errorArgs = ['custom_fields.'.$custom_field->id];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       value="<?php echo e(old('custom_fields.'.$custom_field->id, $custom_field_value)); ?>"
                                       id="custom_field_<?php echo e($custom_field->id); ?>"
                                       <?php if($custom_field->required == 1): ?> required <?php endif; ?>>
                            <?php endif; ?>
                            <?php if($custom_field->type == 'date'): ?>
                                <input type="text" name="custom_fields[<?php echo e($custom_field->id); ?>]"
                                       class="form-control date-picker <?php $__errorArgs = ['custom_fields.'.$custom_field->id];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       value="<?php echo e(old('custom_fields.'.$custom_field->id, $custom_field_value)); ?>"
                                       id="custom_field_<?php echo e($custom_field->id); ?>"
                                       <?php if($custom_field->required == 1): ?> required <?php endif; ?>>
                            <?php endif; ?>
                            <?php if($custom_field->type == 'file'): ?>
                                <input type="file" name="custom_fields[<?php echo e($custom_field->id); ?>]"
                                       class="form-control <?php $__errorArgs = ['custom_fields.'.$custom_field->id];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="custom_field_<?php echo e($custom_field->id); ?>"
                                       <?php if($custom_field->required == 1): ?> required <?php endif; ?>>
                                <?php if(!empty($custom_field_value)): ?>
                                    <small class="form-text text-muted">
                                        Current file: <a href="<?php echo e(asset('storage/uploads/custom_fields/'.$custom_field_value)); ?>" target="_blank"><?php echo e($custom_field_value); ?></a>
                                    </small>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if($custom_field->type == 'textarea'): ?>
                                <textarea name="custom_fields[<?php echo e($custom_field->id); ?>]"
                                         class="form-control <?php $__errorArgs = ['custom_fields.'.$custom_field->id];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                         id="custom_field_<?php echo e($custom_field->id); ?>"
                                         <?php if($custom_field->required == 1): ?> required <?php endif; ?>><?php echo e(old('custom_fields.'.$custom_field->id, $custom_field_value)); ?></textarea>
                            <?php endif; ?>
                            <?php if($custom_field->type == 'number'): ?>
                                <input type="number" name="custom_fields[<?php echo e($custom_field->id); ?>]"
                                       class="form-control <?php $__errorArgs = ['custom_fields.'.$custom_field->id];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       value="<?php echo e(old('custom_fields.'.$custom_field->id, $custom_field_value)); ?>"
                                       id="custom_field_<?php echo e($custom_field->id); ?>"
                                       <?php if($custom_field->required == 1): ?> required <?php endif; ?>>
                            <?php endif; ?>
                            <?php $__errorArgs = ['custom_fields.'.$custom_field->id];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                <strong><?php echo e($message); ?></strong>
                            </span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <div class="card-footer border-top ">
                    <button type="submit"
                            class="btn btn-primary  float-right"><?php echo e(trans_choice('core::general.save',1)); ?></button>
                </div>
            </div>
        </form>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script>
        var app = new Vue({
            el: '#app',
            data: {
                is_client: "<?php echo e(old('is_client',$loan_guarantor->is_client)); ?>",
                client_id: parseInt("<?php echo e(old('client_id',$loan_guarantor->client_id)); ?>"),
                client_relationship_id: parseInt("<?php echo e(old('client_relationship_id',$loan_guarantor->client_relationship_id)); ?>"),
                title_id: parseInt("<?php echo e(old('title_id',$loan_guarantor->title_id)); ?>"),
                first_name: "<?php echo e(old('first_name',$loan_guarantor->first_name)); ?>",
                last_name: "<?php echo e(old('last_name',$loan_guarantor->last_name)); ?>",
                gender: "<?php echo e(old('gender',$loan_guarantor->gender)); ?>",
                marital_status: "<?php echo e(old('marital_status',$loan_guarantor->marital_status)); ?>",
                country_id: parseInt("<?php echo e(old('country_id',$loan_guarantor->country_id)); ?>"),
                mobile: "<?php echo e(old('mobile',$loan_guarantor->mobile)); ?>",
                dob: "<?php echo e(old('dob',$loan_guarantor->dob)); ?>",
                email: "<?php echo e(old('email',$loan_guarantor->email)); ?>",
                profession_id: parseInt("<?php echo e(old('profession_id',$loan_guarantor->profession_id)); ?>"),
                client_type_id: parseInt("<?php echo e(old('client_type_id',$loan_guarantor->client_type_id)); ?>"),
                active: "<?php echo e(old('active',$loan_guarantor->active)); ?>",
                address: `<?php echo e(old('address',$loan_guarantor->address)); ?>`,
                notes: `<?php echo e(old('notes',$loan_guarantor->notes)); ?>`,
                guaranteed_amount: "<?php echo e(old('guaranteed_amount',$loan_guarantor->guaranteed_amount)); ?>",
                clients: <?php echo json_encode($clients); ?>,
                client_relationships:  <?php echo json_encode($client_relationships); ?>,
                titles:  <?php echo json_encode($titles); ?>,
                professions:  <?php echo json_encode($professions); ?>,
                countries:  <?php echo json_encode($countries); ?>,
            },
            created: function () {

            },
            methods: {
                onSubmit() {

                }
            }
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('core::layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\annex\Modules\Loan\Providers/../Resources/views/themes/adminlte/guarantor/edit.blade.php ENDPATH**/ ?>