{"__meta": {"id": "Xaa207469255500a3d0682e9b3bd41bff", "datetime": "2025-08-08 18:32:20", "utime": **********.867853, "method": "GET", "uri": "/annex/public/loan?s=akomanyi", "ip": "**************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754670739.220777, "end": **********.86789, "duration": 1.6471128463745117, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1754670739.220777, "relative_start": 0, "end": **********.235568, "relative_end": **********.235568, "duration": 1.0147910118103027, "duration_str": "1.01s", "params": [], "collector": null}, {"label": "Application", "start": **********.23997, "relative_start": 1.0191929340362549, "end": **********.867894, "relative_end": 4.0531158447265625e-06, "duration": 0.6279239654541016, "duration_str": "628ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 47857272, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "loan::themes.adminlte.loan.index (\\Modules\\Loan\\Resources\\views\\themes\\adminlte\\loan\\index.blade.php)", "param_count": 1, "params": ["data"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\resources\\views\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "core::layouts.master (\\themes\\adminlte\\views\\layouts\\master.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.top_nav (\\themes\\adminlte\\views\\partials\\top_nav.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::menu.admin (\\themes\\adminlte\\views\\menu\\admin.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.flash.message (\\themes\\adminlte\\views\\partials\\flash\\message.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.footer (\\themes\\adminlte\\views\\partials\\footer.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}]}, "route": {"uri": "GET loan", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanController@index", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:62-153"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.32910999999999996, "accumulated_duration_str": "329ms", "statements": [{"sql": "select * from `users` where `id` = 9 limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.01967, "duration_str": "19.67ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 9 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.00484, "duration_str": "4.84ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 9 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select count(*) as aggregate from (select concat(clients.first_name,' ',clients.last_name) client,concat(users.first_name,' ',users.last_name) loan_officer,loans.id,loans.client_id,loans.applied_amount,loans.principal,loans.disbursed_on_date,loans.expected_maturity_date,loan_products.name loan_product,loans.status,loans.decimals,branches.name branch, SUM(loan_repayment_schedules.principal) total_principal, SUM(loan_repayment_schedules.principal_written_off_derived) principal_written_off_derived, SUM(loan_repayment_schedules.principal_repaid_derived) principal_repaid_derived, SUM(loan_repayment_schedules.interest) total_interest, SUM(loan_repayment_schedules.interest_waived_derived) interest_waived_derived,SUM(loan_repayment_schedules.interest_written_off_derived) interest_written_off_derived,  SUM(loan_repayment_schedules.interest_repaid_derived) interest_repaid_derived,SUM(loan_repayment_schedules.fees) total_fees, SUM(loan_repayment_schedules.fees_waived_derived) fees_waived_derived, SUM(loan_repayment_schedules.fees_written_off_derived) fees_written_off_derived, SUM(loan_repayment_schedules.fees_repaid_derived) fees_repaid_derived,SUM(loan_repayment_schedules.penalties) total_penalties, SUM(loan_repayment_schedules.penalties_waived_derived) penalties_waived_derived, SUM(loan_repayment_schedules.penalties_written_off_derived) penalties_written_off_derived, SUM(loan_repayment_schedules.penalties_repaid_derived) penalties_repaid_derived from `loans` left join `clients` on `clients`.`id` = `loans`.`client_id` left join `loan_repayment_schedules` on `loan_repayment_schedules`.`loan_id` = `loans`.`id` left join `loan_products` on `loan_products`.`id` = `loans`.`loan_product_id` left join `branches` on `branches`.`id` = `loans`.`branch_id` left join `users` on `users`.`id` = `loans`.`loan_officer_id` where `due_date` between '2025-08-04 00:00:00' and '2025-08-10 23:59:59' and `loan_products`.`name` like '%akomanyi%' or `clients`.`first_name` like '%akomanyi%' or `clients`.`last_name` like '%akomanyi%' or `loans`.`id` like '%akomanyi%' or `loans`.`account_number` like '%akomanyi%' or `loans`.`external_id` like '%akomanyi%' group by `loans`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["2025-08-04 00:00:00", "2025-08-10 23:59:59", "%akomanyi%", "%akomanyi%", "%akomanyi%", "%akomanyi%", "%akomanyi%", "%akomanyi%"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 107}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.*****************, "duration_str": "158ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:107", "connection": "annex"}, {"sql": "select concat(clients.first_name,' ',clients.last_name) client,concat(users.first_name,' ',users.last_name) loan_officer,loans.id,loans.client_id,loans.applied_amount,loans.principal,loans.disbursed_on_date,loans.expected_maturity_date,loan_products.name loan_product,loans.status,loans.decimals,branches.name branch, SUM(loan_repayment_schedules.principal) total_principal, SUM(loan_repayment_schedules.principal_written_off_derived) principal_written_off_derived, SUM(loan_repayment_schedules.principal_repaid_derived) principal_repaid_derived, SUM(loan_repayment_schedules.interest) total_interest, SUM(loan_repayment_schedules.interest_waived_derived) interest_waived_derived,SUM(loan_repayment_schedules.interest_written_off_derived) interest_written_off_derived,  SUM(loan_repayment_schedules.interest_repaid_derived) interest_repaid_derived,SUM(loan_repayment_schedules.fees) total_fees, SUM(loan_repayment_schedules.fees_waived_derived) fees_waived_derived, SUM(loan_repayment_schedules.fees_written_off_derived) fees_written_off_derived, SUM(loan_repayment_schedules.fees_repaid_derived) fees_repaid_derived,SUM(loan_repayment_schedules.penalties) total_penalties, SUM(loan_repayment_schedules.penalties_waived_derived) penalties_waived_derived, SUM(loan_repayment_schedules.penalties_written_off_derived) penalties_written_off_derived, SUM(loan_repayment_schedules.penalties_repaid_derived) penalties_repaid_derived from `loans` left join `clients` on `clients`.`id` = `loans`.`client_id` left join `loan_repayment_schedules` on `loan_repayment_schedules`.`loan_id` = `loans`.`id` left join `loan_products` on `loan_products`.`id` = `loans`.`loan_product_id` left join `branches` on `branches`.`id` = `loans`.`branch_id` left join `users` on `users`.`id` = `loans`.`loan_officer_id` where `due_date` between '2025-08-04 00:00:00' and '2025-08-10 23:59:59' and `loan_products`.`name` like '%akomanyi%' or `clients`.`first_name` like '%akomanyi%' or `clients`.`last_name` like '%akomanyi%' or `loans`.`id` like '%akomanyi%' or `loans`.`account_number` like '%akomanyi%' or `loans`.`external_id` like '%akomanyi%' group by `loans`.`id` limit 10 offset 0", "type": "query", "params": [], "bindings": ["2025-08-04 00:00:00", "2025-08-10 23:59:59", "%akomanyi%", "%akomanyi%", "%akomanyi%", "%akomanyi%", "%akomanyi%", "%akomanyi%"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 107}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.13572, "duration_str": "136ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:107", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 30}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::partials.top_nav", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "view::core::partials.top_nav:31", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_logo' limit 1", "type": "query", "params": [], "bindings": ["core.company_logo"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 3}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "view::core::menu.admin:3", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_name' limit 1", "type": "query", "params": [], "bindings": ["core.company_name"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 8}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "view::core::menu.admin:8", "connection": "annex"}, {"sql": "select * from `menus` where `is_parent` = 1 order by `menu_order` asc", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0024700000000000004, "duration_str": "2.47ms", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}, {"sql": "select * from `menus` where `menus`.`parent_id` in (1, 9, 10, 12, 13, 17, 18, 21, 24, 28, 31, 36, 37, 44, 49, 54, 58, 61, 66, 71) order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}]}, "models": {"data": {"Modules\\Core\\Entities\\Menu": 73, "Modules\\Setting\\Entities\\Setting": 2, "Modules\\Loan\\Entities\\Loan": 9, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 1}, "count": 86}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<PERSON>@mjy3.com\"\n  \"user\" => array:25 [\n    \"id\" => 9\n    \"created_by_id\" => null\n    \"branch_id\" => null\n    \"name\" => \"\"\n    \"username\" => null\n    \"email\" => \"<PERSON>@mjy3.com\"\n    \"email_verified_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"<PERSON>\"\n    \"phone\" => \"+233555928981\"\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"male\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => \"This access is given for the purpose of internal functions only\"\n    \"photo\" => \"kI83IU45zpa8IywCrNXJY6rulxWk3OSLHuGt0dCg.jpeg\"\n    \"created_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"updated_at\" => \"2025-01-17T08:13:41.000000Z\"\n    \"full_name\" => \"<PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 4\n        \"is_system\" => 0\n        \"name\" => \"Loan Officer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"updated_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 9\n          \"role_id\" => 4\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n    \"unread_notifications\" => []\n  ]\n]", "api": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 41, "messages": [{"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-16299287 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16299287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.415622}, {"message": "[ability => loan.loans.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-656764541 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656764541\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.725593}, {"message": "[ability => dashboard.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dashboard.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.763359}, {"message": "[\n  ability => accounting.chart_of_accounts.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"34 characters\">accounting.chart_of_accounts.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.765369}, {"message": "[\n  ability => accounting.journal_entries.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">accounting.journal_entries.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.766741}, {"message": "[ability => branch.branches.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">branch.branches.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768055}, {"message": "[ability => branch.branches.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1623049578 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">branch.branches.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623049578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.770834}, {"message": "[ability => client.clients.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-106934161 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">client.clients.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106934161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773947}, {"message": "[ability => client.clients.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1817963541 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">client.clients.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817963541\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775638}, {"message": "[ability => payroll.payroll.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1431651156 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll.payroll.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431651156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.777189}, {"message": "[\n  ability => communication.campaigns.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1101372959 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">communication.campaigns.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101372959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.778609}, {"message": "[\n  ability => communication.campaigns.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1082057105 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">communication.campaigns.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082057105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.780253}, {"message": "[\n  ability => communication.logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1444484652 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">communication.logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444484652\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.782284}, {"message": "[\n  ability => communication.sms_gateways.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1191827826 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">communication.sms_gateways.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191827826\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.783908}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-417292908 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-417292908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.785708}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1526173219 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526173219\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789611}, {"message": "[ability => loan.loans.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-450890400 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450890400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.792582}, {"message": "[\n  ability => loan.loans.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-722824026 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722824026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.794143}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1281631641 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281631641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.795546}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1398694807 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398694807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.797191}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-177478752 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177478752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.798696}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-833850894 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833850894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.800573}, {"message": "[ability => reports.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1980051782 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reports.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980051782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.802672}, {"message": "[ability => expense.expenses.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1549270326 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">expense.expenses.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549270326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.805352}, {"message": "[ability => savings.savings.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2106024148 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">savings.savings.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106024148\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.807123}, {"message": "[ability => savings.savings.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1137650904 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">savings.savings.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137650904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.808842}, {"message": "[\n  ability => savings.savings.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-929404664 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">savings.savings.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929404664\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.810604}, {"message": "[\n  ability => savings.savings.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1669703564 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">savings.savings.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669703564\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.812406}, {"message": "[\n  ability => customfield.custom_fields.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1563574064 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">customfield.custom_fields.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563574064\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.814064}, {"message": "[\n  ability => customfield.custom_fields.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1465510129 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customfield.custom_fields.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465510129\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.815539}, {"message": "[ability => income.income.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-758208752 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">income.income.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-758208752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.81699}, {"message": "[ability => user.users.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1616012553 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.users.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616012553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.818425}, {"message": "[ability => user.users.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-771188516 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">user.users.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771188516\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.822149}, {"message": "[ability => user.roles.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1730381438 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.roles.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730381438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.826411}, {"message": "[ability => core.modules.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-46122598 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">core.modules.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46122598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.830425}, {"message": "[ability => asset.assets.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1720479175 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">asset.assets.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720479175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.831945}, {"message": "[ability => share.shares.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1995823362 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">share.shares.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995823362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.833306}, {"message": "[ability => setting.setting.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-338335133 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338335133\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.834542}, {"message": "[ability => core.menu.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-401812162 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">core.menu.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401812162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.836215}, {"message": "[ability => core.themes.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1026208299 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">core.themes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026208299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.840839}, {"message": "[\n  ability => activitylog.activity_logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-920518061 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">activitylog.activity_logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920518061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.845965}]}, "session": {"_token": "FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://*************/annex/public/loan?s=akomanyi\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "9", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://*************/annex/public/_debugbar/telescope/9f961c9e-f4de-4932-84b3-b3ae8b34dd00\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan", "status_code": "<pre class=sf-dump id=sf-dump-1009300186 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1009300186\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-286961853 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp>\n  \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"8 characters\">akomanyi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286961853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-357913851 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp>\n  \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"8 characters\">akomanyi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357913851\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-739540975 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://*************/annex/public/loan</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IlcwY29yOXdYcVNQazZuK3krZ1ZiL3c9PSIsInZhbHVlIjoiaTZ6WXVkZGZQdmYxMkFNNlB4QzIrM3N2NElzbnI3YmJNd01oWDJjd3pabkpKV1ZkaHNoUlZjMWVjVGZsRUJzQVAyQUFjd3M4Z2NzK2JlK0pERVVocEhtUFpDZitMbWhnZHU0MHN4NnorVkRVWno1elE0OWFOckdFNjArVFB1NnQiLCJtYWMiOiI5NWZhYjA4NDJhNjdiMTBmZjdhMmNiYzFjNjQyODM4OGNiMTUzMGIzYjcxMGU0MDcxMDc2MmY4NzBiYTFmNDJkIn0%3D; mjy3_micro_loans_session=eyJpdiI6IlhPdDBkclF3VjJYTTl3U0x1WlAvb3c9PSIsInZhbHVlIjoiUU9aajhrcGM1REFWeklWbU0zNGJvYTZzamVVS0NvSG9WUUtJbHcwdTlxYUlkSXcveTJRcVpBU3NzTFBGc2pUaUxDUjVqbE1vdTJJUWxsZ2QvbnluNFM5MXZ1NHlUWlB2TzhLVDV0UE1xL0JDbzFucHhWYlQ3V2JLb3Zkc21GZ0EiLCJtYWMiOiJhMWNhMjE2MDI0YmZiZDNkODAyYzc0MGNiMDkwMWIyOWI0NjczMThmM2VjNDY4YTQ0NGFkOTA5OGFkYjcyZDk1In0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739540975\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-190263238 data-indent-pad=\"  \"><span class=sf-dump-note>array:51</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://*************/annex/public/loan</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IlcwY29yOXdYcVNQazZuK3krZ1ZiL3c9PSIsInZhbHVlIjoiaTZ6WXVkZGZQdmYxMkFNNlB4QzIrM3N2NElzbnI3YmJNd01oWDJjd3pabkpKV1ZkaHNoUlZjMWVjVGZsRUJzQVAyQUFjd3M4Z2NzK2JlK0pERVVocEhtUFpDZitMbWhnZHU0MHN4NnorVkRVWno1elE0OWFOckdFNjArVFB1NnQiLCJtYWMiOiI5NWZhYjA4NDJhNjdiMTBmZjdhMmNiYzFjNjQyODM4OGNiMTUzMGIzYjcxMGU0MDcxMDc2MmY4NzBiYTFmNDJkIn0%3D; mjy3_micro_loans_session=eyJpdiI6IlhPdDBkclF3VjJYTTl3U0x1WlAvb3c9PSIsInZhbHVlIjoiUU9aajhrcGM1REFWeklWbU0zNGJvYTZzamVVS0NvSG9WUUtJbHcwdTlxYUlkSXcveTJRcVpBU3NzTFBGc2pUaUxDUjVqbE1vdTJJUWxsZ2QvbnluNFM5MXZ1NHlUWlB2TzhLVDV0UE1xL0JDbzFucHhWYlQ3V2JLb3Zkc21GZ0EiLCJtYWMiOiJhMWNhMjE2MDI0YmZiZDNkODAyYzc0MGNiMDkwMWIyOWI0NjczMThmM2VjNDY4YTQ0NGFkOTA5OGFkYjcyZDk1In0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"99 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at ************* Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"14 characters\">**************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">34391</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/annex/public/loan</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">s=akomanyi</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">s=akomanyi</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/annex/public/loan?s=akomanyi</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754670739.2208</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754670739</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190263238\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-74573345 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yPsec4PrJ2v0O17ETRQJnf9a8nwYk8R7zJ9UNPPA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74573345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-648054781 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 16:32:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6IjRKQ25JUFpUQkhWSERmMjJrcHNGRmc9PSIsInZhbHVlIjoiZ09jYm9KNlpPNVl3MndobTV3elJLUGtjM0dIT2c4aldIcDZSK2hNOG92RE8rdCtadEwremlJeE9KakdIYUYzUURVV1N6dlpBdUVHYTdxWm1BSktuYjI3LzQyMk83Mm9yc0FocTU3d3ZsQ1M2a0t0MmdFMkhESFM3K0NUNkpBRnoiLCJtYWMiOiIxZDBhOWU3ZGE5MmEyYjFhYTJjMjcwYWM2OGRiNDVkYzFmNmZlZDE5MTNmNTM5NWYxM2NmNDkyYWE2N2UwNmI4In0%3D; expires=Sat, 09-Aug-2025 12:32:20 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6ImkxMG9BM2kxZ1pBQ0U3VTZOMmo4V1E9PSIsInZhbHVlIjoiVmV0MEg2eU5SWDdFb2pvMGJRdUlOTHVSR1pQVUJEOFBTNFhkUy9DNnRCeFhhQnRGVFV5STdhWWpiMVAzSjk3ZlZIV0pVejFORzhndkVRam5SMDErWTdvUk03ei9sTVNwQngwdGhGZm4wc09LZURSUHJHL215THRTQnh0aEFTbWgiLCJtYWMiOiJjZWNkYWU3MzhmNGM5YzY1NzI5MjllNDlmYmViNTliNTZmYjNkMGQ4MjMwZDk5NzMxY2NjMmIxZTJkNDRmNzFhIn0%3D; expires=Sat, 09-Aug-2025 12:32:20 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6IjRKQ25JUFpUQkhWSERmMjJrcHNGRmc9PSIsInZhbHVlIjoiZ09jYm9KNlpPNVl3MndobTV3elJLUGtjM0dIT2c4aldIcDZSK2hNOG92RE8rdCtadEwremlJeE9KakdIYUYzUURVV1N6dlpBdUVHYTdxWm1BSktuYjI3LzQyMk83Mm9yc0FocTU3d3ZsQ1M2a0t0MmdFMkhESFM3K0NUNkpBRnoiLCJtYWMiOiIxZDBhOWU3ZGE5MmEyYjFhYTJjMjcwYWM2OGRiNDVkYzFmNmZlZDE5MTNmNTM5NWYxM2NmNDkyYWE2N2UwNmI4In0%3D; expires=Sat, 09-Aug-2025 12:32:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6ImkxMG9BM2kxZ1pBQ0U3VTZOMmo4V1E9PSIsInZhbHVlIjoiVmV0MEg2eU5SWDdFb2pvMGJRdUlOTHVSR1pQVUJEOFBTNFhkUy9DNnRCeFhhQnRGVFV5STdhWWpiMVAzSjk3ZlZIV0pVejFORzhndkVRam5SMDErWTdvUk03ei9sTVNwQngwdGhGZm4wc09LZURSUHJHL215THRTQnh0aEFTbWgiLCJtYWMiOiJjZWNkYWU3MzhmNGM5YzY1NzI5MjllNDlmYmViNTliNTZmYjNkMGQ4MjMwZDk5NzMxY2NjMmIxZTJkNDRmNzFhIn0%3D; expires=Sat, 09-Aug-2025 12:32:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648054781\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1458786811 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://*************/annex/public/loan?s=akomanyi</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458786811\", {\"maxDepth\":0})</script>\n"}}