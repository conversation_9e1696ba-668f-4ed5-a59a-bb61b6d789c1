{"__meta": {"id": "X71a1de38588ed717b50d5325f003eb77", "datetime": "2025-08-08 18:43:32", "utime": **********.184224, "method": "GET", "uri": "/annex/public/report", "ip": "**************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754671411.219943, "end": **********.184264, "duration": 0.9643208980560303, "duration_str": "964ms", "measures": [{"label": "Booting", "start": 1754671411.219943, "relative_start": 0, "end": 1754671411.935354, "relative_end": 1754671411.935354, "duration": 0.7154109477996826, "duration_str": "715ms", "params": [], "collector": null}, {"label": "Application", "start": 1754671411.937889, "relative_start": 0.7179460525512695, "end": **********.184266, "relative_end": 2.1457672119140625e-06, "duration": 0.24637699127197266, "duration_str": "246ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 46867824, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "report::themes.adminlte.index (\\Modules\\Report\\Resources\\views\\themes\\adminlte\\index.blade.php)", "param_count": 1, "params": ["data"], "type": "blade"}, {"name": "core::layouts.master (\\themes\\adminlte\\views\\layouts\\master.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "keys", "loop", "value", "key"], "type": "blade"}, {"name": "core::partials.top_nav (\\themes\\adminlte\\views\\partials\\top_nav.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "keys", "loop", "value", "key"], "type": "blade"}, {"name": "core::menu.admin (\\themes\\adminlte\\views\\menu\\admin.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "keys", "loop", "value", "key"], "type": "blade"}, {"name": "core::partials.flash.message (\\themes\\adminlte\\views\\partials\\flash\\message.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "keys", "loop", "value", "key"], "type": "blade"}, {"name": "core::partials.footer (\\themes\\adminlte\\views\\partials\\footer.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "keys", "loop", "value", "key"], "type": "blade"}]}, "route": {"uri": "GET report", "middleware": "web", "controller": "Modules\\Report\\Http\\Controllers\\ReportController@index", "namespace": "Modules\\Report\\Http\\Controllers", "prefix": "/report", "where": [], "file": "\\Modules\\Report\\Http\\Controllers\\ReportController.php:26-39"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.045529999999999994, "accumulated_duration_str": "45.53ms", "statements": [{"sql": "select * from `users` where `id` = 9 limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.026449999999999998, "duration_str": "26.45ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 9 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.01084, "duration_str": "10.84ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 9 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 30}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::partials.top_nav", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "view::core::partials.top_nav:31", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_logo' limit 1", "type": "query", "params": [], "bindings": ["core.company_logo"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 3}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "view::core::menu.admin:3", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_name' limit 1", "type": "query", "params": [], "bindings": ["core.company_name"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 8}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "view::core::menu.admin:8", "connection": "annex"}, {"sql": "select * from `menus` where `is_parent` = 1 order by `menu_order` asc", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}, {"sql": "select * from `menus` where `menus`.`parent_id` in (1, 9, 10, 12, 13, 17, 18, 21, 24, 28, 31, 36, 37, 44, 49, 54, 58, 61, 66, 71) order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}]}, "models": {"data": {"Modules\\Core\\Entities\\Menu": 73, "Modules\\Setting\\Entities\\Setting": 2, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 1}, "count": 77}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<PERSON>@mjy3.com\"\n  \"user\" => array:25 [\n    \"id\" => 9\n    \"created_by_id\" => null\n    \"branch_id\" => null\n    \"name\" => \"\"\n    \"username\" => null\n    \"email\" => \"<PERSON>@mjy3.com\"\n    \"email_verified_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"<PERSON>\"\n    \"phone\" => \"+233555928981\"\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"male\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => \"This access is given for the purpose of internal functions only\"\n    \"photo\" => \"kI83IU45zpa8IywCrNXJY6rulxWk3OSLHuGt0dCg.jpeg\"\n    \"created_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"updated_at\" => \"2025-01-17T08:13:41.000000Z\"\n    \"full_name\" => \"<PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 4\n        \"is_system\" => 0\n        \"name\" => \"Loan Officer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"updated_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 9\n          \"role_id\" => 4\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n    \"unread_notifications\" => []\n  ]\n]", "api": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 40, "messages": [{"message": "[ability => reports.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1027015407 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reports.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027015407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076788}, {"message": "[ability => dashboard.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dashboard.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.113773}, {"message": "[\n  ability => accounting.chart_of_accounts.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"34 characters\">accounting.chart_of_accounts.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.115826}, {"message": "[\n  ability => accounting.journal_entries.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">accounting.journal_entries.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.117164}, {"message": "[ability => branch.branches.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">branch.branches.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.118446}, {"message": "[ability => branch.branches.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-44696996 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">branch.branches.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44696996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.119722}, {"message": "[ability => client.clients.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1011459293 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">client.clients.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011459293\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.121017}, {"message": "[ability => client.clients.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-45771485 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">client.clients.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45771485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122275}, {"message": "[ability => payroll.payroll.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-325173432 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll.payroll.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325173432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.123541}, {"message": "[\n  ability => communication.campaigns.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-399458848 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">communication.campaigns.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399458848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.124736}, {"message": "[\n  ability => communication.campaigns.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1064917196 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">communication.campaigns.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064917196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.125924}, {"message": "[\n  ability => communication.logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-32758262 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">communication.logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32758262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.127111}, {"message": "[\n  ability => communication.sms_gateways.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1085828452 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">communication.sms_gateways.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085828452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.128281}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-83328830 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83328830\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.129486}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-873157615 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873157615\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.131545}, {"message": "[ability => loan.loans.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1183453442 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183453442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.133015}, {"message": "[\n  ability => loan.loans.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1253623756 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253623756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.134474}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-932278299 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932278299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135885}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-201102059 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201102059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137289}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-824886380 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824886380\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.138824}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-646725440 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646725440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.140502}, {"message": "[ability => reports.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-703726722 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reports.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703726722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.142409}, {"message": "[ability => expense.expenses.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-877679139 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">expense.expenses.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877679139\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.143974}, {"message": "[ability => savings.savings.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-29524569 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">savings.savings.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29524569\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.145423}, {"message": "[ability => savings.savings.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-416804842 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">savings.savings.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416804842\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.147096}, {"message": "[\n  ability => savings.savings.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-881219969 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">savings.savings.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881219969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.148693}, {"message": "[\n  ability => savings.savings.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-560398551 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">savings.savings.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560398551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.150259}, {"message": "[\n  ability => customfield.custom_fields.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1779576608 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">customfield.custom_fields.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779576608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.1519}, {"message": "[\n  ability => customfield.custom_fields.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1192742211 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customfield.custom_fields.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192742211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.153528}, {"message": "[ability => income.income.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1902770510 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">income.income.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902770510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.155433}, {"message": "[ability => user.users.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-968027041 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.users.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-968027041\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.15788}, {"message": "[ability => user.users.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2140110311 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">user.users.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2140110311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.160176}, {"message": "[ability => user.roles.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-134876459 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.roles.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134876459\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.161783}, {"message": "[ability => core.modules.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-610946243 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">core.modules.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610946243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.163773}, {"message": "[ability => asset.assets.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-794434004 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">asset.assets.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794434004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.165531}, {"message": "[ability => share.shares.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1119794659 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">share.shares.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119794659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.166933}, {"message": "[ability => setting.setting.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-55428192 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55428192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.168242}, {"message": "[ability => core.menu.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-363143420 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">core.menu.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363143420\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.169546}, {"message": "[ability => core.themes.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2118925295 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">core.themes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118925295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.170863}, {"message": "[\n  ability => activitylog.activity_logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1340937173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">activitylog.activity_logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340937173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.172224}]}, "session": {"_token": "FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://*************/annex/public/report\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "9", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://*************/annex/public/_debugbar/telescope/9f96209f-47ce-41eb-97d1-3032d694bf3a\" target=\"_blank\">View in Telescope</a>", "path_info": "/report", "status_code": "<pre class=sf-dump id=sf-dump-1287981732 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1287981732\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1463551107 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1463551107\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-302704477 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-302704477\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1712975026 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://*************/annex/public/loan</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6InRlZFgwTW50NGxoeFZkcDYvbmZEeGc9PSIsInZhbHVlIjoiSW9SSS9uU255UHhlVGUzVkdCRnhhSnVBWDJjZ0svYlZWVWF2Q0Q3eEF2bUdPZ1p1T2NObW54SUl4SDY5UUx0elNJWFFKUk1FcGZpM20vK1YxZzQ2dDNyb0YrbDVScWJkbC9ROTg5NThnbWk1UWVOY1M2NnB6MG9VZS84TlRuUFEiLCJtYWMiOiI0OGFjMTVmZTQ0OWFiZDBiNTE5YjIyMjc1ZjY1OTEwNzk0Nzg5MTkyMzdiOGYyMTVjNjdhZGZmNjcwYzI4OTkwIn0%3D; mjy3_micro_loans_session=eyJpdiI6ImltekpuSE9tOUsvOW56d1NvMWk2Y0E9PSIsInZhbHVlIjoiY1hNa05SZmY0QUUyTTJnVU9tSk5pV1ZqbjhxcGVKQi9vUlYxKzdhcWNlcmNrVHRNek1BNkdlQ3FjRDdXbHB4cTRzVzhBem9YMDlUa1VmUitYTzV4WHBkemVsY0lLams1NStBenl4MVVualhId204U2czMGxTUGRtSFhUSTZLZlgiLCJtYWMiOiI5NTI0MmQ0NmNiNDAyNDA1MTBhMDA0YTkxNmRkODk3YWMyNWQ4ZmE1YjQ5NTZjMjViYjY5N2ExNDNhYjVjOTRkIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712975026\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-190604918 data-indent-pad=\"  \"><span class=sf-dump-note>array:50</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://*************/annex/public/loan</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6InRlZFgwTW50NGxoeFZkcDYvbmZEeGc9PSIsInZhbHVlIjoiSW9SSS9uU255UHhlVGUzVkdCRnhhSnVBWDJjZ0svYlZWVWF2Q0Q3eEF2bUdPZ1p1T2NObW54SUl4SDY5UUx0elNJWFFKUk1FcGZpM20vK1YxZzQ2dDNyb0YrbDVScWJkbC9ROTg5NThnbWk1UWVOY1M2NnB6MG9VZS84TlRuUFEiLCJtYWMiOiI0OGFjMTVmZTQ0OWFiZDBiNTE5YjIyMjc1ZjY1OTEwNzk0Nzg5MTkyMzdiOGYyMTVjNjdhZGZmNjcwYzI4OTkwIn0%3D; mjy3_micro_loans_session=eyJpdiI6ImltekpuSE9tOUsvOW56d1NvMWk2Y0E9PSIsInZhbHVlIjoiY1hNa05SZmY0QUUyTTJnVU9tSk5pV1ZqbjhxcGVKQi9vUlYxKzdhcWNlcmNrVHRNek1BNkdlQ3FjRDdXbHB4cTRzVzhBem9YMDlUa1VmUitYTzV4WHBkemVsY0lLams1NStBenl4MVVualhId204U2czMGxTUGRtSFhUSTZLZlgiLCJtYWMiOiI5NTI0MmQ0NmNiNDAyNDA1MTBhMDA0YTkxNmRkODk3YWMyNWQ4ZmE1YjQ5NTZjMjViYjY5N2ExNDNhYjVjOTRkIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"99 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at ************* Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"14 characters\">**************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">34977</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/annex/public/report</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/annex/public/report</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754671411.2199</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754671411</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190604918\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-778395539 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yPsec4PrJ2v0O17ETRQJnf9a8nwYk8R7zJ9UNPPA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-778395539\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1188444850 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 16:43:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6IkVjUlRIOForTVlCNUMvWm5CMnlOdEE9PSIsInZhbHVlIjoiVWhEUUc4bjJENU9UNnU3ZS93Z2NpSjFSRlZJTTBnTmNlTHRBNCtJSVUrOERlSUJYak1HcXJ4WGhVQk9BY28wcCszS05DMGVnVkZKMmRJeERLMVR4SU8vZ2EraHZPVk9IUjJqUHk4anhaTUY5MURrZE5QWncvdjhQYlZpelZ3b0kiLCJtYWMiOiJjODU4ZGM5OTQzNWQzNzZjMzM2YWEwYTIzYTA0NTFiMjY3OWU2M2FiNDQwYTg2ZTViZDhmMTY4MDUyMTZjZDczIn0%3D; expires=Sat, 09-Aug-2025 12:43:32 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6IjNINEpEUmw1bzNnVW5OR1REUkJva3c9PSIsInZhbHVlIjoiaFR2S2twWEZheG5ybzBLN1ZQL3MxczlSRCtZTE1wT1ZHZmE5Mmx1RnVXSGNEeE1WYS9OdngxN21wem9XaCs4UjRwSEJ5WnJqNyt2ZVRHUGlWWTZCMWJCalhDcjlic1lVSnBkRjBQWHZFWTZCdzR4REN4SXJGSDJxZG5KWVgwcmwiLCJtYWMiOiI4YjFjM2U1YTdkY2QwOTZhZTlkMzU2N2YwODM3ZWVlYmZkMGI5N2U1NTg4MzFjODMxZDhhMzhjYjFlNWE0ZGY4In0%3D; expires=Sat, 09-Aug-2025 12:43:32 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6IkVjUlRIOForTVlCNUMvWm5CMnlOdEE9PSIsInZhbHVlIjoiVWhEUUc4bjJENU9UNnU3ZS93Z2NpSjFSRlZJTTBnTmNlTHRBNCtJSVUrOERlSUJYak1HcXJ4WGhVQk9BY28wcCszS05DMGVnVkZKMmRJeERLMVR4SU8vZ2EraHZPVk9IUjJqUHk4anhaTUY5MURrZE5QWncvdjhQYlZpelZ3b0kiLCJtYWMiOiJjODU4ZGM5OTQzNWQzNzZjMzM2YWEwYTIzYTA0NTFiMjY3OWU2M2FiNDQwYTg2ZTViZDhmMTY4MDUyMTZjZDczIn0%3D; expires=Sat, 09-Aug-2025 12:43:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6IjNINEpEUmw1bzNnVW5OR1REUkJva3c9PSIsInZhbHVlIjoiaFR2S2twWEZheG5ybzBLN1ZQL3MxczlSRCtZTE1wT1ZHZmE5Mmx1RnVXSGNEeE1WYS9OdngxN21wem9XaCs4UjRwSEJ5WnJqNyt2ZVRHUGlWWTZCMWJCalhDcjlic1lVSnBkRjBQWHZFWTZCdzR4REN4SXJGSDJxZG5KWVgwcmwiLCJtYWMiOiI4YjFjM2U1YTdkY2QwOTZhZTlkMzU2N2YwODM3ZWVlYmZkMGI5N2U1NTg4MzFjODMxZDhhMzhjYjFlNWE0ZGY4In0%3D; expires=Sat, 09-Aug-2025 12:43:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188444850\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2090522510 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://*************/annex/public/report</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090522510\", {\"maxDepth\":0})</script>\n"}}