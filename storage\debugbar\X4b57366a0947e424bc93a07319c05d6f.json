{"__meta": {"id": "X4b57366a0947e424bc93a07319c05d6f", "datetime": "2025-08-08 18:27:10", "utime": **********.400934, "method": "GET", "uri": "/annex/public/loan/guarantor/564/show", "ip": "*************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754670428.322652, "end": **********.400957, "duration": 2.0783050060272217, "duration_str": "2.08s", "measures": [{"label": "Booting", "start": 1754670428.322652, "relative_start": 0, "end": 1754670429.110279, "relative_end": 1754670429.110279, "duration": 0.7876269817352295, "duration_str": "788ms", "params": [], "collector": null}, {"label": "Application", "start": 1754670429.112574, "relative_start": 0.7899219989776611, "end": **********.400959, "relative_end": 1.9073486328125e-06, "duration": 1.2883849143981934, "duration_str": "1.29s", "params": [], "collector": null}]}, "memory": {"peak_usage": 47454304, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "loan::themes.adminlte.guarantor.show (\\Modules\\Loan\\Resources\\views\\themes\\adminlte\\guarantor\\show.blade.php)", "param_count": 2, "params": ["loan_guarantor", "custom_fields"], "type": "blade"}, {"name": "core::layouts.master (\\themes\\adminlte\\views\\layouts\\master.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan_guarantor", "custom_fields", "__currentLoopData", "custom_field", "loop", "custom_field_meta"], "type": "blade"}, {"name": "core::partials.top_nav (\\themes\\adminlte\\views\\partials\\top_nav.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan_guarantor", "custom_fields", "__currentLoopData", "custom_field", "loop", "custom_field_meta"], "type": "blade"}, {"name": "core::menu.admin (\\themes\\adminlte\\views\\menu\\admin.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan_guarantor", "custom_fields", "__currentLoopData", "custom_field", "loop", "custom_field_meta"], "type": "blade"}, {"name": "core::partials.flash.message (\\themes\\adminlte\\views\\partials\\flash\\message.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan_guarantor", "custom_fields", "__currentLoopData", "custom_field", "loop", "custom_field_meta"], "type": "blade"}, {"name": "core::partials.footer (\\themes\\adminlte\\views\\partials\\footer.blade.php)", "param_count": 12, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan_guarantor", "custom_fields", "__currentLoopData", "custom_field", "loop", "custom_field_meta"], "type": "blade"}]}, "route": {"uri": "GET loan/guarantor/{id}/show", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanGuarantorController@show", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:131-136"}, "queries": {"nb_statements": 25, "nb_failed_statements": 0, "accumulated_duration": 0.06227000000000002, "accumulated_duration_str": "62.27ms", "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00177, "duration_str": "1.77ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0132, "duration_str": "13.2ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["1", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0022, "duration_str": "2.2ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.00382, "duration_str": "3.82ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["1", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.0016200000000000001, "duration_str": "1.62ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select * from `loan_guarantors` where `loan_guarantors`.`id` = '564' limit 1", "type": "query", "params": [], "bindings": ["564"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00174, "duration_str": "1.74ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `loans` where `loans`.`id` in (697)", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0020099999999999996, "duration_str": "2.01ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `clients` where `clients`.`id` in (27)", "type": "query", "params": [], "bindings": ["27"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00177, "duration_str": "1.77ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `loan_products` where `loan_products`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00161, "duration_str": "1.61ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `titles` where `titles`.`id` in (1)", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `professions` where `professions`.`id` in (10)", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `countries` where `countries`.`id` in (83)", "type": "query", "params": [], "bindings": ["83"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `client_relationships` where `client_relationships`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `clients` where `clients`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 133}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:133", "connection": "annex"}, {"sql": "select * from `custom_fields` where `category` = 'add_guarantor' and `active` = 1", "type": "query", "params": [], "bindings": ["add_guarantor", "1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php", "line": 134}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanGuarantorController.php:134", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where `category` = 'add_guarantor' and `parent_id` = 564 and `custom_field_id` = 21 limit 1", "type": "query", "params": [], "bindings": ["add_guarantor", "564", "21"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "loan::themes.adminlte.guarantor.show", "line": 340}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.009800000000000001, "duration_str": "9.8ms", "stmt_id": "view::loan::themes.adminlte.guarantor.show:340", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where `category` = 'add_guarantor' and `parent_id` = 564 and `custom_field_id` = 22 limit 1", "type": "query", "params": [], "bindings": ["add_guarantor", "564", "22"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "loan::themes.adminlte.guarantor.show", "line": 340}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00671, "duration_str": "6.71ms", "stmt_id": "view::loan::themes.adminlte.guarantor.show:340", "connection": "annex"}, {"sql": "select * from `users` where `users`.`id` = 9 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.guarantor.show", "line": 499}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00181, "duration_str": "1.81ms", "stmt_id": "view::loan::themes.adminlte.guarantor.show:499", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["1", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00173, "duration_str": "1.73ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["1", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 30}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `notifications` where `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["1", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::partials.top_nav", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "view::core::partials.top_nav:31", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_logo' limit 1", "type": "query", "params": [], "bindings": ["core.company_logo"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 3}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "view::core::menu.admin:3", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_name' limit 1", "type": "query", "params": [], "bindings": ["core.company_name"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 8}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "view::core::menu.admin:8", "connection": "annex"}, {"sql": "select * from `menus` where `is_parent` = 1 order by `menu_order` asc", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}, {"sql": "select * from `menus` where `menus`.`parent_id` in (1, 9, 10, 12, 13, 17, 18, 21, 24, 28, 31, 36, 37, 44, 49, 54, 58, 61, 66, 71) order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}]}, "models": {"data": {"Modules\\Core\\Entities\\Menu": 73, "Modules\\Setting\\Entities\\Setting": 2, "Modules\\CustomField\\Entities\\CustomFieldMeta": 2, "Modules\\CustomField\\Entities\\CustomField": 2, "Modules\\Client\\Entities\\ClientRelationship": 1, "Modules\\Core\\Entities\\Country": 1, "Modules\\Client\\Entities\\Profession": 1, "Modules\\Client\\Entities\\Title": 1, "Modules\\Loan\\Entities\\LoanProduct": 1, "Modules\\Client\\Entities\\Client": 1, "Modules\\Loan\\Entities\\Loan": 1, "Modules\\Loan\\Entities\\LoanGuarantor": 1, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 2}, "count": 90}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"admin\"\n  \"user\" => array:25 [\n    \"id\" => 1\n    \"created_by_id\" => null\n    \"branch_id\" => 1\n    \"name\" => \"Admin\"\n    \"username\" => \"admin\"\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2022-06-06T13:23:25.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"Quintin\"\n    \"last_name\" => \"Kweku\"\n    \"phone\" => null\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"male\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => null\n    \"photo\" => \"mMuwmDyxpyNjnFBRkttEO1NRGogDJyT8gNGTiezP.jpeg\"\n    \"created_at\" => \"2022-06-06T13:23:25.000000Z\"\n    \"updated_at\" => \"2023-05-26T13:08:30.000000Z\"\n    \"full_name\" => \"Quin<PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 1\n        \"is_system\" => 1\n        \"name\" => \"admin\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T13:23:25.000000Z\"\n        \"updated_at\" => \"2022-06-06T13:23:25.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 1\n          \"role_id\" => 1\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n    \"unread_notifications\" => []\n  ]\n]", "api": "null"}, "names": "web: admin"}, "gate": {"count": 68, "messages": [{"message": "[\n  ability => loan.loans.guarantors.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-729326777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">loan.loans.guarantors.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729326777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754670429.249753}, {"message": "[\n  ability => loan.loans.guarantors.edit,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2067411517 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">loan.loans.guarantors.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067411517\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.220658}, {"message": "[\n  ability => loan.loans.guarantors.destroy,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-849957194 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">loan.loans.guarantors.destroy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849957194\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.222575}, {"message": "[ability => dashboard.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dashboard.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.282763}, {"message": "[\n  ability => accounting.chart_of_accounts.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"34 characters\">accounting.chart_of_accounts.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.284542}, {"message": "[\n  ability => accounting.journal_entries.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">accounting.journal_entries.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.28636}, {"message": "[ability => branch.branches.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">branch.branches.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.287946}, {"message": "[ability => branch.branches.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1694903169 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">branch.branches.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694903169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.289388}, {"message": "[ability => client.clients.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-583549248 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">client.clients.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583549248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.290753}, {"message": "[ability => client.clients.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1336892442 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">client.clients.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336892442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.292251}, {"message": "[ability => payroll.payroll.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1468407513 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll.payroll.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468407513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.293765}, {"message": "[ability => payroll.payroll.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-456372574 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll.payroll.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456372574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.295244}, {"message": "[ability => payroll.payroll.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1833621563 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">payroll.payroll.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833621563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296682}, {"message": "[\n  ability => payroll.payroll.items.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1875643272 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">payroll.payroll.items.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875643272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.298185}, {"message": "[\n  ability => payroll.payroll.templates.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-210000824 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">payroll.payroll.templates.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210000824\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.29953}, {"message": "[\n  ability => communication.campaigns.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-879607623 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">communication.campaigns.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879607623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.301194}, {"message": "[\n  ability => communication.campaigns.create,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-284772015 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">communication.campaigns.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284772015\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.302745}, {"message": "[\n  ability => communication.logs.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-885529436 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">communication.logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885529436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304194}, {"message": "[\n  ability => communication.sms_gateways.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1563620555 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">communication.sms_gateways.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563620555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.30558}, {"message": "[ability => loan.loans.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1630222735 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630222735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.307061}, {"message": "[ability => loan.loans.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1080420829 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080420829\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.308765}, {"message": "[ability => loan.loans.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-459327866 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459327866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.310411}, {"message": "[\n  ability => loan.loans.products.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1882131292 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882131292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311954}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-493215833 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493215833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.313484}, {"message": "[ability => loan.loans.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1419913329 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419913329\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.314966}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1130091710 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130091710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316363}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1046303281 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046303281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317733}, {"message": "[ability => reports.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-217292548 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reports.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217292548\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.319165}, {"message": "[ability => expense.expenses.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1798048635 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">expense.expenses.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798048635\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.320527}, {"message": "[ability => expense.expenses.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-977614026 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">expense.expenses.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977614026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.322002}, {"message": "[\n  ability => expense.expenses.create,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1857068940 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">expense.expenses.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857068940\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.323872}, {"message": "[\n  ability => expense.expenses.types.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-747539779 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">expense.expenses.types.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747539779\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.325447}, {"message": "[ability => savings.savings.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1095754748 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">savings.savings.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095754748\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.327031}, {"message": "[ability => savings.savings.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-10055966 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">savings.savings.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10055966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328347}, {"message": "[\n  ability => savings.savings.products.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-477267898 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">savings.savings.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-477267898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.329612}, {"message": "[\n  ability => savings.savings.charges.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1283458806 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">savings.savings.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283458806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.33087}, {"message": "[\n  ability => customfield.custom_fields.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1556171780 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">customfield.custom_fields.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556171780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.332173}, {"message": "[\n  ability => customfield.custom_fields.create,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-396777503 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customfield.custom_fields.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396777503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.333431}, {"message": "[ability => income.income.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1214066463 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">income.income.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214066463\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.334696}, {"message": "[ability => income.income.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1706241284 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">income.income.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706241284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.335912}, {"message": "[ability => income.income.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-887224359 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">income.income.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887224359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.337145}, {"message": "[\n  ability => income.income.types.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2024764510 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">income.income.types.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024764510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.338751}, {"message": "[ability => user.users.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-37589760 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.users.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37589760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.341737}, {"message": "[ability => user.users.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1768362754 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">user.users.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768362754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.344107}, {"message": "[ability => user.roles.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1298534280 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.roles.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298534280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.345509}, {"message": "[ability => core.modules.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-395618578 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">core.modules.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395618578\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.346891}, {"message": "[ability => asset.assets.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1481276050 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">asset.assets.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481276050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.348212}, {"message": "[ability => asset.assets.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-210127000 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">asset.assets.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210127000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.349576}, {"message": "[ability => asset.assets.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-413019449 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">asset.assets.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413019449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.350937}, {"message": "[\n  ability => asset.assets.types.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1501235908 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">asset.assets.types.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501235908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.352327}, {"message": "[ability => share.shares.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1340427221 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">share.shares.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340427221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.354177}, {"message": "[ability => share.shares.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-728947960 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">share.shares.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728947960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.355577}, {"message": "[ability => share.shares.create, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-130666356 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">share.shares.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130666356\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356939}, {"message": "[\n  ability => share.shares.products.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1737123746 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">share.shares.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737123746\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.358806}, {"message": "[\n  ability => share.shares.charges.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-724006431 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">share.shares.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724006431\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.3602}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2085654045 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085654045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.361718}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1270574605 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270574605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.363042}, {"message": "[\n  ability => core.payment_gateways.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-388253637 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">core.payment_gateways.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388253637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.364322}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-824688282 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824688282\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.365641}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1828480130 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828480130\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.370168}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1532956858 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532956858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.371963}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-551166307 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551166307\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.373976}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-26647689 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26647689\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.376467}, {"message": "[ability => setting.setting.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-959446456 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959446456\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378781}, {"message": "[ability => upgrade.upgrades.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1794657448 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">upgrade.upgrades.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794657448\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.381035}, {"message": "[ability => core.menu.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-412234704 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">core.menu.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412234704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.383375}, {"message": "[ability => core.themes.index, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-862091527 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">core.themes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862091527\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.387118}, {"message": "[\n  ability => activitylog.activity_logs.index,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1210634048 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">activitylog.activity_logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210634048\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.38924}]}, "session": {"_token": "BDnw2z3HYFycnztWa5AjP1tqvRAJrGhgE6Cug2GV", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://mjy3.relandbek.com/annex/public/loan/guarantor/564/show\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"https://mjy3.relandbek.com/annex/public/_debugbar/telescope/9f961ac5-337e-4c5c-bd89-20a581c83d71\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan/guarantor/564/show", "status_code": "<pre class=sf-dump id=sf-dump-1803370932 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1803370932\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-579277614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-579277614\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-832467588 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-832467588\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">https://mjy3.relandbek.com/annex/public/loan/697/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6InVJanZMd1U1emVpejg1SjNVNFVta3c9PSIsInZhbHVlIjoiMlJoU3cvTTRhRU5VcExkTWdmTGJRVHMrVFRnbjczenhsT0c3djk0cjlHVUc3UnJCUGQwZFBGbTBDMnA5TlB1QWRwckJlRHhUcFkvdkNRd1BwaG1WbUVqREcvOU4xMXhkVURRZWlZaFVhZDAyM0o2RVJyV25laFlFM3Z0Y2Qzd1EiLCJtYWMiOiIzZjdhYTZlYjM0Y2ZhMGYyODA0ZWYxMWFhMGQwNDc4ZTI1NGVkMWU1NzU4NmNmMDQzMmYyM2EyNDdkMmZhMmZjIn0%3D; mjy3_micro_loans_session=eyJpdiI6Ik41bjJaNjdicjZFVWlMM2duZllORHc9PSIsInZhbHVlIjoiUTF1SjJEYzRRWmhDRzNiWWsrKzFIcWJkako2NmZEUnh6VkpaSVdTSkFLL3Jmb3liRkVDVVdjZkxGejVHS212YWVmWnh3aG9KNkdHK0hmeXBBbmp3aDgyckRyZDhnS09RaGwrL1R0MXJwQVRuckdLeUFwOFpvNmEzWXQ4d0l2d1EiLCJtYWMiOiI1Y2FkOWRjNDBlMjBiNjk5ZGJiYzhkMWE1MzY1MzQ0ZjA5OTgxNmFjOWU1ZmUyZjc4OWYzNzk5OTRjYTI4OGY3In0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:87</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_S_DN_CN</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_C</span>\" => \"<span class=sf-dump-str title=\"2 characters\">AT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_O</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ZeroSSL</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_CN</span>\" => \"<span class=sf-dump-str title=\"33 characters\">ZeroSSL RSA Domain Secure Site CA</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_SAN_DNS_0</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_VERSION_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">mod_ssl/2.4.48</span>\"\n  \"<span class=sf-dump-key>SSL_VERSION_LIBRARY</span>\" => \"<span class=sf-dump-str title=\"14 characters\">OpenSSL/1.1.1k</span>\"\n  \"<span class=sf-dump-key>SSL_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TLSv1.3</span>\"\n  \"<span class=sf-dump-key>SSL_SECURE_RENEG</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>SSL_COMPRESS_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">NULL</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">TLS_AES_256_GCM_SHA384</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_EXPORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_USEKEYSIZE</span>\" => \"<span class=sf-dump-str title=\"3 characters\">256</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_ALGKEYSIZE</span>\" => \"<span class=sf-dump-str title=\"3 characters\">256</span>\"\n  \"<span class=sf-dump-key>SSL_CLIENT_VERIFY</span>\" => \"<span class=sf-dump-str title=\"4 characters\">NONE</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_M_VERSION</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_M_SERIAL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">9915F93C899342A4AF7869D230B4DEA4</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_V_START</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Jul 29 00:00:00 2025 GMT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_V_END</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Oct 27 23:59:59 2025 GMT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_S_DN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">CN=mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN</span>\" => \"<span class=sf-dump-str title=\"51 characters\">CN=ZeroSSL RSA Domain Secure Site CA,O=ZeroSSL,C=AT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_A_KEY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_A_SIG</span>\" => \"<span class=sf-dump-str title=\"23 characters\">sha384WithRSAEncryption</span>\"\n  \"<span class=sf-dump-key>SSL_SESSION_ID</span>\" => \"<span class=sf-dump-str title=\"64 characters\">2e5da57496c8ab3a44fe50bc9d0255620ddbd2a550207d003974d83cc060d7a4</span>\"\n  \"<span class=sf-dump-key>SSL_SESSION_RESUMED</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Initial</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"53 characters\">https://mjy3.relandbek.com/annex/public/loan/697/show</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6InVJanZMd1U1emVpejg1SjNVNFVta3c9PSIsInZhbHVlIjoiMlJoU3cvTTRhRU5VcExkTWdmTGJRVHMrVFRnbjczenhsT0c3djk0cjlHVUc3UnJCUGQwZFBGbTBDMnA5TlB1QWRwckJlRHhUcFkvdkNRd1BwaG1WbUVqREcvOU4xMXhkVURRZWlZaFVhZDAyM0o2RVJyV25laFlFM3Z0Y2Qzd1EiLCJtYWMiOiIzZjdhYTZlYjM0Y2ZhMGYyODA0ZWYxMWFhMGQwNDc4ZTI1NGVkMWU1NzU4NmNmMDQzMmYyM2EyNDdkMmZhMmZjIn0%3D; mjy3_micro_loans_session=eyJpdiI6Ik41bjJaNjdicjZFVWlMM2duZllORHc9PSIsInZhbHVlIjoiUTF1SjJEYzRRWmhDRzNiWWsrKzFIcWJkako2NmZEUnh6VkpaSVdTSkFLL3Jmb3liRkVDVVdjZkxGejVHS212YWVmWnh3aG9KNkdHK0hmeXBBbmp3aDgyckRyZDhnS09RaGwrL1R0MXJwQVRuckdLeUFwOFpvNmEzWXQ4d0l2d1EiLCJtYWMiOiI1Y2FkOWRjNDBlMjBiNjk5ZGJiYzhkMWE1MzY1MzQ0ZjA5OTgxNmFjOWU1ZmUyZjc4OWYzNzk5OTRjYTI4OGY3In0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"105 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at mjy3.relandbek.com Port 443&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12797</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/annex/public/loan/guarantor/564/show</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/annex/public/loan/guarantor/564/show</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754670428.3227</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754670428</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1593549197 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BDnw2z3HYFycnztWa5AjP1tqvRAJrGhgE6Cug2GV</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LUvyKRf9TZjoIrXwRu9xc8Vt2J7ZVuxzNoCBChfI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593549197\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-565816966 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 16:27:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6IlVPdHNHQnptbjNONkZ1Sis2ZVl4enc9PSIsInZhbHVlIjoiVTFVakJlYmREVG9jT2Q3cE5RUjZQZ0NVOS81M0toaEVTSDhvdktpTGFrejJsQ0NMSlVlZklHKzROSkpqZDJyQWNWSFlqc0ZlMC93Qzh6cEk4eFBiVS9GZzFEUFdrR0ZKUS9BbFdlbE55d3VGSzJlNzdNS3hhVXRBbWtieXZwWFoiLCJtYWMiOiJkNDAyYzQ3NzVkODI4MDVjYWY1MTg5YTZiYzA3NmJmODhhNWQ3YjJjZGZiMzMwNzE3MGRiODlmZjQxNTlkZTRlIn0%3D; expires=Sat, 09-Aug-2025 12:27:10 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6Ik1FYU1hMDVzdXRybGo4cGltL3hVTlE9PSIsInZhbHVlIjoiaVhpZUhTd2Z3MXBmbnhQUWhwVFNvTzZwNFEwKzFMVUdYNFA5QTJUbjNFQkxiQmRiamtlcXZUd0IvdXRhR0tCVDkvL0V5Ym5qMXhUZTVYYUFtTkMwU0VleFFzclRoc1pOZi9ESTJOUVBSMURUNExtcGFUOUJxWktYUzdPdFpDL3AiLCJtYWMiOiI5MjE2M2U1YTdiYTU3NzQ2ZTA5NjE3YTgzMmY0YzA5NDgyMTQwMWU2OTkwMzk1OWMzYWIzM2U5NjYxZDA5ODk3In0%3D; expires=Sat, 09-Aug-2025 12:27:10 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6IlVPdHNHQnptbjNONkZ1Sis2ZVl4enc9PSIsInZhbHVlIjoiVTFVakJlYmREVG9jT2Q3cE5RUjZQZ0NVOS81M0toaEVTSDhvdktpTGFrejJsQ0NMSlVlZklHKzROSkpqZDJyQWNWSFlqc0ZlMC93Qzh6cEk4eFBiVS9GZzFEUFdrR0ZKUS9BbFdlbE55d3VGSzJlNzdNS3hhVXRBbWtieXZwWFoiLCJtYWMiOiJkNDAyYzQ3NzVkODI4MDVjYWY1MTg5YTZiYzA3NmJmODhhNWQ3YjJjZGZiMzMwNzE3MGRiODlmZjQxNTlkZTRlIn0%3D; expires=Sat, 09-Aug-2025 12:27:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6Ik1FYU1hMDVzdXRybGo4cGltL3hVTlE9PSIsInZhbHVlIjoiaVhpZUhTd2Z3MXBmbnhQUWhwVFNvTzZwNFEwKzFMVUdYNFA5QTJUbjNFQkxiQmRiamtlcXZUd0IvdXRhR0tCVDkvL0V5Ym5qMXhUZTVYYUFtTkMwU0VleFFzclRoc1pOZi9ESTJOUVBSMURUNExtcGFUOUJxWktYUzdPdFpDL3AiLCJtYWMiOiI5MjE2M2U1YTdiYTU3NzQ2ZTA5NjE3YTgzMmY0YzA5NDgyMTQwMWU2OTkwMzk1OWMzYWIzM2U5NjYxZDA5ODk3In0%3D; expires=Sat, 09-Aug-2025 12:27:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565816966\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1136789153 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BDnw2z3HYFycnztWa5AjP1tqvRAJrGhgE6Cug2GV</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">https://mjy3.relandbek.com/annex/public/loan/guarantor/564/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136789153\", {\"maxDepth\":0})</script>\n"}}