{"__meta": {"id": "Xc68872b4f5bebc446b89c00769faf040", "datetime": "2025-08-08 17:51:06", "utime": 1754668266.493198, "method": "GET", "uri": "/annex/public/login", "ip": "*************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754668265.816668, "end": 1754668266.493238, "duration": 0.676569938659668, "duration_str": "677ms", "measures": [{"label": "Booting", "start": 1754668265.816668, "relative_start": 0, "end": 1754668266.434849, "relative_end": 1754668266.434849, "duration": 0.6181809902191162, "duration_str": "618ms", "params": [], "collector": null}, {"label": "Application", "start": 1754668266.436582, "relative_start": 0.6199140548706055, "end": 1754668266.49324, "relative_end": 2.1457672119140625e-06, "duration": 0.056658029556274414, "duration_str": "56.66ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 25925304, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "user::themes.adminlte.auth.login (\\Modules\\User\\Resources\\views\\themes\\adminlte\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade"}, {"name": "core::layouts.auth (\\themes\\adminlte\\views\\layouts\\auth.blade.php)", "param_count": 7, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "logo"], "type": "blade"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "Modules\\User\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "Modules\\User\\Http\\Controllers", "prefix": null, "where": [], "as": "login", "file": "\\Modules\\User\\Http\\Controllers\\Auth\\LoginController.php:46-49"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0019299999999999999, "accumulated_duration_str": "1.93ms", "statements": [{"sql": "select * from `settings` where `setting_key` = 'core.company_logo' limit 1", "type": "query", "params": [], "bindings": ["core.company_logo"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "user::themes.adminlte.auth.login", "line": 9}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "view::user::themes.adminlte.auth.login:9", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_name' limit 1", "type": "query", "params": [], "bindings": ["core.company_name"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "user::themes.adminlte.auth.login", "line": 14}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "view::user::themes.adminlte.auth.login:14", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'user.enable_registration' limit 1", "type": "query", "params": [], "bindings": ["user.enable_registration"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "user::themes.adminlte.auth.login", "line": 106}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "view::user::themes.adminlte.auth.login:106", "connection": "annex"}]}, "models": {"data": {"Modules\\Setting\\Entities\\Setting": 3}, "count": 3}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "api": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sox8SZxaY0mvUlQancTZSUxgdalJUH0I5eP0ITCV", "url": "array:1 [\n  \"intended\" => \"https://mjy3.relandbek.com/annex/public\"\n]", "_previous": "array:1 [\n  \"url\" => \"https://mjy3.relandbek.com/annex/public/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"https://mjy3.relandbek.com/annex/public/_debugbar/telescope/9f960ddf-54c9-42df-bd17-f45b57e6e3d7\" target=\"_blank\">View in Telescope</a>", "path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1809302168 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1809302168\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1276093090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1276093090\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1152304742 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1152304742\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1613775552 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IlEvV3ZpbDlMYkZzWnJ5b0JxZ05pMlE9PSIsInZhbHVlIjoiR0oyK0NwY0plT1IvRGZ0LzROS2dsanNWQzBYdW9VaVBZcHBFdGNKTzlSeVpHWEFKbEtaZW9UYmRnc2QzNUZhZ1kzdjgxeGVFSklIU0dBTUs1cm95SlVTajJmWmFkT0svODZRcDFXZzNLNXZRanZoZDFKVE83MFpKRTJ5T0F1TkIiLCJtYWMiOiIzN2I5NDk3NGJhMTA5Mzc2Nzc0MTc1ZjZhOGFjMTE0NmVmZTA2OTliZTM0ZWExMjg1YzNmNTJkMWQwMDUwNWJlIn0%3D; mjy3_micro_loans_session=eyJpdiI6InlEMC9OZzVGb3o4VnhQdzVzQkpOb1E9PSIsInZhbHVlIjoiOHEydEs1Wnd6Wi9OdTVQUTl2TmhjWmc1bmpJYWJxNVptR0ZDMVdhSUhRNVh6czJjRjRRZFRmUXdUZlZyU1ZiYk4xTUF5N29XQ25nam1jSTVZNzlSbU5QV2NIdnhHN0dTR1VJc2FrK091dVRMdmE3QVJTanRSdW9aYjN2eSs1M0oiLCJtYWMiOiI1ODgxZmI2OGYyYjZiNDhhNjk0NTExOGNlNzJjYjA4NzQ1OGFhNTc2MmQ5NzczYjNhNDk2MjI2Mzc1NzQ0ODZkIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1613775552\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1022585464 data-indent-pad=\"  \"><span class=sf-dump-note>array:85</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_S_DN_CN</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_C</span>\" => \"<span class=sf-dump-str title=\"2 characters\">AT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_O</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ZeroSSL</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN_CN</span>\" => \"<span class=sf-dump-str title=\"33 characters\">ZeroSSL RSA Domain Secure Site CA</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_SAN_DNS_0</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_VERSION_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">mod_ssl/2.4.48</span>\"\n  \"<span class=sf-dump-key>SSL_VERSION_LIBRARY</span>\" => \"<span class=sf-dump-str title=\"14 characters\">OpenSSL/1.1.1k</span>\"\n  \"<span class=sf-dump-key>SSL_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TLSv1.3</span>\"\n  \"<span class=sf-dump-key>SSL_SECURE_RENEG</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>SSL_COMPRESS_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">NULL</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">TLS_AES_256_GCM_SHA384</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_EXPORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_USEKEYSIZE</span>\" => \"<span class=sf-dump-str title=\"3 characters\">256</span>\"\n  \"<span class=sf-dump-key>SSL_CIPHER_ALGKEYSIZE</span>\" => \"<span class=sf-dump-str title=\"3 characters\">256</span>\"\n  \"<span class=sf-dump-key>SSL_CLIENT_VERIFY</span>\" => \"<span class=sf-dump-str title=\"4 characters\">NONE</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_M_VERSION</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_M_SERIAL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">9915F93C899342A4AF7869D230B4DEA4</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_V_START</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Jul 29 00:00:00 2025 GMT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_V_END</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Oct 27 23:59:59 2025 GMT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_S_DN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">CN=mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_I_DN</span>\" => \"<span class=sf-dump-str title=\"51 characters\">CN=ZeroSSL RSA Domain Secure Site CA,O=ZeroSSL,C=AT</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_A_KEY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>SSL_SERVER_A_SIG</span>\" => \"<span class=sf-dump-str title=\"23 characters\">sha384WithRSAEncryption</span>\"\n  \"<span class=sf-dump-key>SSL_SESSION_ID</span>\" => \"<span class=sf-dump-str title=\"64 characters\">9bbc17f8ab7e8f0924f90fe4d16d72f43cda46c3f5fae07d3616faa2d41c3141</span>\"\n  \"<span class=sf-dump-key>SSL_SESSION_RESUMED</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Initial</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IlEvV3ZpbDlMYkZzWnJ5b0JxZ05pMlE9PSIsInZhbHVlIjoiR0oyK0NwY0plT1IvRGZ0LzROS2dsanNWQzBYdW9VaVBZcHBFdGNKTzlSeVpHWEFKbEtaZW9UYmRnc2QzNUZhZ1kzdjgxeGVFSklIU0dBTUs1cm95SlVTajJmWmFkT0svODZRcDFXZzNLNXZRanZoZDFKVE83MFpKRTJ5T0F1TkIiLCJtYWMiOiIzN2I5NDk3NGJhMTA5Mzc2Nzc0MTc1ZjZhOGFjMTE0NmVmZTA2OTliZTM0ZWExMjg1YzNmNTJkMWQwMDUwNWJlIn0%3D; mjy3_micro_loans_session=eyJpdiI6InlEMC9OZzVGb3o4VnhQdzVzQkpOb1E9PSIsInZhbHVlIjoiOHEydEs1Wnd6Wi9OdTVQUTl2TmhjWmc1bmpJYWJxNVptR0ZDMVdhSUhRNVh6czJjRjRRZFRmUXdUZlZyU1ZiYk4xTUF5N29XQ25nam1jSTVZNzlSbU5QV2NIdnhHN0dTR1VJc2FrK091dVRMdmE3QVJTanRSdW9aYjN2eSs1M0oiLCJtYWMiOiI1ODgxZmI2OGYyYjZiNDhhNjk0NTExOGNlNzJjYjA4NzQ1OGFhNTc2MmQ5NzczYjNhNDk2MjI2Mzc1NzQ0ODZkIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"105 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at mjy3.relandbek.com Port 443&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"18 characters\">mjy3.relandbek.com</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11575</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/annex/public/login</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/annex/public/login</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754668265.8167</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754668265</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022585464\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1342389640 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sox8SZxaY0mvUlQancTZSUxgdalJUH0I5eP0ITCV</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HZ78Vyt1cpB0sd1V72zoc5ODpqCjOlN0YwUDEpeX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342389640\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-93096129 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 15:51:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6IjJZWCswZEt5c2lwWHVPcDQvU2JkRnc9PSIsInZhbHVlIjoiKzJjMjJtT3BLUHRJMzlFVHpUL3lza2ducHZJK1cxbS9kRGNRL0dZUi9OSmFMREFsajM5RW5HZ2FnaHRtSGQ2TXNhUjhacWhpV0JrUnBTNzJaVWVwOUNyK2s0N1V3OHhHazhkT3d2L3lZM21pa1RHd2pKcnNuRDN0ZmtOWkI5a2giLCJtYWMiOiIwMTg2ODVjODBhNDRkN2NiNDFhZGNlNWE0ZWNlODgzMmM5NDMzMmY3ODE1Y2ZiMzA4ZTE5ZDcxZTZkZjdmZWZkIn0%3D; expires=Sat, 09-Aug-2025 11:51:06 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6IjVCemxSUTBnQ0FTM3hvYjdPZk8yYVE9PSIsInZhbHVlIjoiYkI0c2JVcmoxTnRISitNMWRuN3NJMXp4TDlwSXV2SWJ1dGI4OUVJRjZmWm1yNWUyOXAydXpwWW1QbTFkQUIyS1VyeWVzQU1BQUdnbjR2MENobXkzWWYwNTYvL2k2ZVgwaGtYVWlPclZxaTdQSlh6eUJza0dZdUZDeVpwTFU3UjQiLCJtYWMiOiI5YjNhOTUwMzQ4ZWRmMGFmZjc2MTQxYWRmYjczOGQ1OWZkMzMxNGVkODUyNWFhNWUyY2MyODMzNjJhMDJkNzkyIn0%3D; expires=Sat, 09-Aug-2025 11:51:06 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6IjJZWCswZEt5c2lwWHVPcDQvU2JkRnc9PSIsInZhbHVlIjoiKzJjMjJtT3BLUHRJMzlFVHpUL3lza2ducHZJK1cxbS9kRGNRL0dZUi9OSmFMREFsajM5RW5HZ2FnaHRtSGQ2TXNhUjhacWhpV0JrUnBTNzJaVWVwOUNyK2s0N1V3OHhHazhkT3d2L3lZM21pa1RHd2pKcnNuRDN0ZmtOWkI5a2giLCJtYWMiOiIwMTg2ODVjODBhNDRkN2NiNDFhZGNlNWE0ZWNlODgzMmM5NDMzMmY3ODE1Y2ZiMzA4ZTE5ZDcxZTZkZjdmZWZkIn0%3D; expires=Sat, 09-Aug-2025 11:51:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6IjVCemxSUTBnQ0FTM3hvYjdPZk8yYVE9PSIsInZhbHVlIjoiYkI0c2JVcmoxTnRISitNMWRuN3NJMXp4TDlwSXV2SWJ1dGI4OUVJRjZmWm1yNWUyOXAydXpwWW1QbTFkQUIyS1VyeWVzQU1BQUdnbjR2MENobXkzWWYwNTYvL2k2ZVgwaGtYVWlPclZxaTdQSlh6eUJza0dZdUZDeVpwTFU3UjQiLCJtYWMiOiI5YjNhOTUwMzQ4ZWRmMGFmZjc2MTQxYWRmYjczOGQ1OWZkMzMxNGVkODUyNWFhNWUyY2MyODMzNjJhMDJkNzkyIn0%3D; expires=Sat, 09-Aug-2025 11:51:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93096129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1035518650 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sox8SZxaY0mvUlQancTZSUxgdalJUH0I5eP0ITCV</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://mjy3.relandbek.com/annex/public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://mjy3.relandbek.com/annex/public/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035518650\", {\"maxDepth\":0})</script>\n"}}