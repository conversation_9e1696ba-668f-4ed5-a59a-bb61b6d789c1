@extends('core::layouts.master')
@section('title')
    {{ $loan_guarantor->first_name }} {{ $loan_guarantor->last_name }}
@endsection
@section('styles')
<style>
    .custom-field-value {
        margin-top: 5px;
    }
    .guarantor-profile-img {
        max-width: 150px;
        max-height: 150px;
        border-radius: 50%;
    }
    .guarantor-status {
        font-size: 14px;
        margin-top: 10px;
    }
    .loan-info-table td {
        border: none;
        padding: 8px 12px;
    }
    .loan-info-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    @media (max-width: 768px) {
        .col-md-3, .col-md-9 {
            margin-bottom: 20px;
        }
    }
</style>
@endsection
@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-user"></i>
                        {{trans_choice('loan::general.guarantor',1)}} {{trans_choice('core::general.detail',2)}}
                        @if(!empty($loan_guarantor->title))
                            - {{$loan_guarantor->title->name}}
                        @endif
                        {{$loan_guarantor->first_name}} {{$loan_guarantor->last_name}}
                    </h3>
                    <div class="box-tools pull-right">
                        @can('loan.loans.guarantors.edit')
                            <a href="{{url('loan/guarantor/'.$loan_guarantor->id.'/edit')}}" class="btn btn-info btn-sm">
                                <i class="fa fa-edit"></i> {{trans_choice('core::general.edit',1)}}
                            </a>
                        @endcan
                        @can('loan.loans.guarantors.destroy')
                            <a href="{{url('loan/guarantor/'.$loan_guarantor->id.'/delete')}}" class="btn btn-danger btn-sm confirm">
                                <i class="fa fa-trash"></i> {{trans_choice('core::general.delete',1)}}
                            </a>
                        @endcan
                        @if(!empty($loan_guarantor->loan))
                            <a href="{{url('loan/'.$loan_guarantor->loan_id.'/show')}}" class="btn btn-default btn-sm">
                                <i class="fa fa-arrow-left"></i> Back to {{trans_choice('loan::general.loan',1)}}
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">

            <!-- Profile Image -->
            <div class="box box-primary">
                <div class="box-body box-profile">
                    @if(!empty($loan_guarantor->photo))
                        <a href="{{asset('storage/uploads/loans/'.$loan_guarantor->photo)}}"
                           class="fancybox">
                            <img
                                    class="profile-user-img img-fluid img-circle guarantor-profile-img"
                                    src="{{asset('storage/uploads/loans/'.$loan_guarantor->photo)}}"
                                    alt="User profile picture">
                        </a>
                    @else
                        <img class="profile-user-img img-responsive img-circle guarantor-profile-img"
                             src="{{asset('assets/dist/img/user.png')}}"
                             alt="User profile picture">
                    @endif
                    <h3 class="profile-username text-center">
                        @if(!empty($loan_guarantor->title))
                            {{$loan_guarantor->title->name}}
                        @endif
                        {{$loan_guarantor->first_name}} {{$loan_guarantor->last_name}}</h3>

                    <p class="text-muted text-center">
                        @if(!empty($loan_guarantor->profession->name))
                            {{$loan_guarantor->profession->name}}
                        @endif
                    </p>

                    <div class="text-center guarantor-status">
                        <span class="label label-{{$loan_guarantor->status == 'active' ? 'success' : ($loan_guarantor->status == 'pending' ? 'warning' : 'default')}}">
                            {{ucfirst($loan_guarantor->status)}} {{trans_choice('loan::general.guarantor',1)}}
                        </span>
                        @if(!empty($loan_guarantor->guaranteed_amount))
                            <br><small class="text-muted">
                                Amount Guaranteed:
                                <strong>{{number_format($loan_guarantor->guaranteed_amount, 2)}}</strong>
                            </small>
                        @endif
                    </div>

                    <ul class="list-group list-group-unbordered">
                        <li class="list-group-item">
                            <b>{{trans_choice('client::general.relationship',1)}}</b>
                            <a class="pull-right">
                                @if(!empty($loan_guarantor->client_relationship))
                                    {{$loan_guarantor->client_relationship->name}}
                                @endif
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>{{trans_choice('core::general.mobile',1)}}</b>
                            <a class="pull-right">
                                {{$loan_guarantor->mobile}}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>{{trans_choice('core::general.email',1)}}</b>
                            <a class="pull-right">
                                {{$loan_guarantor->email}}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>{{trans_choice('core::general.dob',1)}}</b>
                            <a class="pull-right">
                                {{$loan_guarantor->dob}}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>{{trans_choice('core::general.gender',1)}}</b>
                            <a class="pull-right">
                                @if($loan_guarantor->gender=='male')
                                    {{trans_choice('core::general.male',1)}}
                                @endif
                                @if($loan_guarantor->gender=='female')
                                    {{trans_choice('core::general.female',1)}}
                                @endif
                                @if($loan_guarantor->gender=='unspecified')
                                    {{trans_choice('core::general.unspecified',1)}}
                                @endif
                                @if($loan_guarantor->gender=='other')
                                    {{trans_choice('core::general.other',1)}}
                                @endif
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>{{trans_choice('client::general.marital_status',1)}}</b>
                            <a class="pull-right">
                                @if($loan_guarantor->marital_status=='single')
                                    {{trans_choice('client::general.single',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='married')
                                    {{trans_choice('client::general.married',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='divorced')
                                    {{trans_choice('client::general.divorced',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='widowed')
                                    {{trans_choice('client::general.widowed',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='other')
                                    {{trans_choice('client::general.other',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='unspecified')
                                    {{trans_choice('core::general.unspecified',1)}}
                                @endif
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>{{trans_choice('core::general.zip',1)}}</b>
                            <a class="pull-right">
                                {{$loan_guarantor->zip}}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>{{trans_choice('core::general.status',1)}}</b>
                            <a class="pull-right">
                                <span class="label label-{{$loan_guarantor->status == 'active' ? 'success' : ($loan_guarantor->status == 'pending' ? 'warning' : 'default')}}">
                                    {{ucfirst($loan_guarantor->status)}}
                                </span>
                            </a>
                        </li>
                        <li class="list-group-item">
                            <b>Guaranteed Amount</b>
                            <a class="pull-right">
                                @if(!empty($loan_guarantor->guaranteed_amount))
                                    {{number_format($loan_guarantor->guaranteed_amount, 2)}}
                                @else
                                    {{trans_choice('core::general.not_specified',1)}}
                                @endif
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->

            <!-- About Me Box -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">{{trans_choice('core::general.extra',1)}} {{trans_choice('core::general.detail',2)}}</h3>
                </div>
                <!-- /.box-header -->
                <div class="box-body">

                    <strong><i class="fa fa-map-marker margin-r-5"></i> {{trans_choice('core::general.address',1)}}
                    </strong>

                    <p class="text-muted">
                        {{$loan_guarantor->address}}<br>
                        @if(!empty($loan_guarantor->country))
                            {{$loan_guarantor->country->name}}
                        @endif
                    </p>
                    <hr>

                    <strong><i class="fa fa-file-text-o margin-r-5"></i> {{trans_choice('core::general.note',2)}}
                    </strong>

                    <p> {{$loan_guarantor->notes}}</p>

                    @if($custom_fields->count() > 0)
                        <hr>
                        <strong><i class="fa fa-list margin-r-5"></i> {{trans_choice('customfield::general.custom_field',2)}}
                        </strong>
                        <div class="row">
                            @foreach($custom_fields as $custom_field)
                                <?php
                                $custom_field_meta = \Modules\CustomField\Entities\CustomFieldMeta::where('category', 'add_guarantor')
                                    ->where('parent_id', $loan_guarantor->id)
                                    ->where('custom_field_id', $custom_field->id)
                                    ->first();
                                ?>
                                @if(!empty($custom_field_meta))
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>{{$custom_field->name}}:</label>
                                            <div class="custom-field-value">
                                                @if($custom_field->type == 'file' && !empty($custom_field_meta->value))
                                                    <a href="{{asset('storage/uploads/custom_fields/'.$custom_field_meta->value)}}"
                                                       target="_blank" class="btn btn-sm btn-default">
                                                        <i class="fa fa-download"></i> {{$custom_field_meta->value}}
                                                    </a>
                                                @elseif($custom_field->type == 'date' && !empty($custom_field_meta->value))
                                                    <span class="text-muted">
                                                        <i class="fa fa-calendar"></i> {{date('Y-m-d', strtotime($custom_field_meta->value))}}
                                                    </span>
                                                @elseif($custom_field->type == 'checkbox')
                                                    <span class="label label-{{$custom_field_meta->value ? 'success' : 'default'}}">
                                                        {{$custom_field_meta->value ? trans_choice('core::general.yes',1) : trans_choice('core::general.no',1)}}
                                                    </span>
                                                @else
                                                    <span class="text-info">{{$custom_field_meta->value}}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @endif
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->
        </div>
        <!-- /.col -->

        <div class="col-md-9">
            <!-- Loan Information Box -->
            @if(!empty($loan_guarantor->loan))
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-money"></i> {{trans_choice('loan::general.loan',1)}} Information
                    </h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>{{trans_choice('loan::general.loan',1)}} {{trans_choice('core::general.id',1)}}:</strong></td>
                                    <td>
                                        <a href="{{url('loan/'.$loan_guarantor->loan->id.'/show')}}">
                                            #{{$loan_guarantor->loan->id}}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('loan::general.product',1)}}:</strong></td>
                                    <td>
                                        @if(!empty($loan_guarantor->loan->loan_product))
                                            {{$loan_guarantor->loan->loan_product->name}}
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.amount',1)}}:</strong></td>
                                    <td>{{number_format($loan_guarantor->loan->principal, 2)}}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.status',1)}}:</strong></td>
                                    <td>
                                        <span class="label label-{{$loan_guarantor->loan->status == 'active' ? 'success' : ($loan_guarantor->loan->status == 'pending' ? 'warning' : 'default')}}">
                                            {{ucfirst($loan_guarantor->loan->status)}}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Client Name</strong></td>
                                    <td>
                                        @if(!empty($loan_guarantor->loan->client))
                                            <a href="{{url('client/'.$loan_guarantor->loan->client->id.'/show')}}">
                                                {{$loan_guarantor->loan->client->first_name}} {{$loan_guarantor->loan->client->last_name}}
                                            </a>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Disbursedd Date</strong></td>
                                    <td>{{$loan_guarantor->loan->disbursed_on_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Maturity Date</strong></td>
                                    <td>{{$loan_guarantor->loan->expected_maturity_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Interest Rate</strong></td>
                                    <td>{{$loan_guarantor->loan->interest_rate}}%</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Additional Information Box -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-info-circle"></i> Additional Details
                    </h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Loan Officer:</strong></td>
                                    <td>{{$loan_guarantor->employer}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone No.</strong></td>
                                    <td>{{$loan_guarantor->phone}}</td>
                                </tr>
                                <tr>
                                    <td><strong>City:</strong></td>
                                    <td>{{$loan_guarantor->city}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Region:</strong></td>
                                    <td>{{$loan_guarantor->state}}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Created Date:</strong></td>
                                    <td>{{$loan_guarantor->created_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Joined Date:</strong></td>
                                    <td>{{$loan_guarantor->joined_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.created_by',1)}}:</strong></td>
                                    <td>
                                        @if(!empty($loan_guarantor->created_by))
                                            {{$loan_guarantor->created_by->first_name}} {{$loan_guarantor->created_by->last_name}}
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.created_at',1)}}:</strong></td>
                                    <td>{{$loan_guarantor->created_at}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.col -->
    </div>
@endsection
@section('scripts')
    <script>
        $('.data-table').DataTable({
            "language": {
                "lengthMenu": "{{ trans('general.lengthMenu') }}",
                "zeroRecords": "{{ trans('general.zeroRecords') }}",
                "info": "{{ trans('general.info') }}",
                "infoEmpty": "{{ trans('general.infoEmpty') }}",
                "search": "{{ trans('general.search') }}",
                "infoFiltered": "{{ trans('general.infoFiltered') }}",
                "paginate": {
                    "first": "{{ trans('general.first') }}",
                    "last": "{{ trans('general.last') }}",
                    "next": "{{ trans('general.next') }}",
                    "previous": "{{ trans('general.previous') }}"
                },
                "columnDefs": [
                    {"orderable": false, "targets": 0}
                ]
            },
            responsive: true
        });
    </script>
@endsection
