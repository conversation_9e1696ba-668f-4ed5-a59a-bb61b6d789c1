@extends('core::layouts.master')
@section('title')
    {{ $loan_guarantor->first_name }} {{ $loan_guarantor->last_name }}
@endsection
@section('styles')
<style>
    .guarantor-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        border: none;
        overflow: hidden;
    }
    .guarantor-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px 10px 0 0;
        border: none;
    }
    .guarantor-profile-section {
        text-align: center;
        padding: 30px 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 10px;
    }
    .guarantor-profile-img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid white;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        margin-bottom: 15px;
    }
    .guarantor-name {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    .guarantor-profession {
        color: #7f8c8d;
        font-size: 16px;
        margin-bottom: 15px;
    }
    .guarantor-status-badge {
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 20px;
        margin-bottom: 10px;
        display: inline-block;
        color: white;
        font-weight: 600;
    }
    .badge-success {
        background-color: #28a745;
    }
    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }
    .badge-secondary {
        background-color: #6c757d;
    }
    .info-card {
        border-radius: 8px;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }
    .info-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 2px solid #e9ecef;
        font-weight: 600;
        color: #495057;
    }
    .info-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .info-list li {
        padding: 12px 20px;
        border-bottom: 1px solid #f1f3f4;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .info-list li:last-child {
        border-bottom: none;
    }
    .info-list li:nth-child(even) {
        background-color: #f8f9fa;
    }
    .info-label {
        font-weight: 600;
        color: #495057;
    }
    .info-value {
        color: #6c757d;
        text-align: right;
    }
    .loan-info-table {
        margin-bottom: 0;
    }
    .loan-info-table td {
        border: none;
        padding: 12px 15px;
        vertical-align: middle;
    }
    .loan-info-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    .custom-field-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 15px;
    }
    .custom-field-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    .custom-field-value {
        color: #6c757d;
    }
    .card-tools .btn {
        margin-left: 5px;
    }
    .d-flex {
        display: flex !important;
    }
    .justify-content-between {
        justify-content: space-between !important;
    }
    .align-items-center {
        align-items: center !important;
    }
    .justify-content-center {
        justify-content: center !important;
    }
    .mt-3 {
        margin-top: 1rem !important;
    }
    .mb-3 {
        margin-bottom: 1rem !important;
    }
    .p-0 {
        padding: 0 !important;
    }
    @media (max-width: 768px) {
        .col-md-4, .col-md-8, .col-md-10 {
            margin-bottom: 20px;
        }
        .guarantor-name {
            font-size: 20px;
        }
        .card-tools {
            flex-direction: column;
            gap: 5px;
        }
        .card-tools .btn {
            margin-left: 0;
            margin-bottom: 5px;
        }
    }
</style>
@endsection
@section('content')
    <div class="container-fluid">
        <!-- Header Card -->
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card guarantor-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fa fa-user"></i>
                            {{trans_choice('loan::general.guarantor',1)}} {{trans_choice('core::general.detail',2)}}
                            @if(!empty($loan_guarantor->title))
                                - {{$loan_guarantor->title->name}}
                            @endif
                            {{$loan_guarantor->first_name}} {{$loan_guarantor->last_name}}
                        </h3>
                        <div class="card-tools">
                            @can('loan.loans.guarantors.edit')
                                <a href="{{url('loan/guarantor/'.$loan_guarantor->id.'/edit')}}" class="btn btn-light btn-sm">
                                    <i class="fa fa-edit"></i> {{trans_choice('core::general.edit',1)}}
                                </a>
                            @endcan
                            @can('loan.loans.guarantors.destroy')
                                <a href="{{url('loan/guarantor/'.$loan_guarantor->id.'/delete')}}" class="btn btn-outline-light btn-sm confirm">
                                    <i class="fa fa-trash"></i> {{trans_choice('core::general.delete',1)}}
                                </a>
                            @endcan
                            @if(!empty($loan_guarantor->loan))
                                <a href="{{url('loan/'.$loan_guarantor->loan_id.'/show')}}" class="btn btn-outline-light btn-sm">
                                    <i class="fa fa-arrow-left"></i> Back to {{trans_choice('loan::general.loan',1)}}
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="row">
                    <div class="col-md-4">
            <!-- Profile Card -->
            <div class="card info-card">
                <div class="guarantor-profile-section">
                    @if(!empty($loan_guarantor->photo))
                        <a href="{{asset('storage/uploads/loans/'.$loan_guarantor->photo)}}" class="fancybox">
                            <img class="guarantor-profile-img"
                                 src="{{asset('storage/uploads/loans/'.$loan_guarantor->photo)}}"
                                 alt="User profile picture">
                        </a>
                    @else
                        <img class="guarantor-profile-img"
                             src="{{asset('assets/dist/img/user.png')}}"
                             alt="User profile picture">
                    @endif

                    <div class="guarantor-name">
                        @if(!empty($loan_guarantor->title))
                            {{$loan_guarantor->title->name}}
                        @endif
                        {{$loan_guarantor->first_name}} {{$loan_guarantor->last_name}}
                    </div>

                    <div class="guarantor-profession">
                        @if(!empty($loan_guarantor->profession->name))
                            {{$loan_guarantor->profession->name}}
                        @endif
                    </div>

                    <div class="guarantor-status-badge badge-{{$loan_guarantor->status == 'active' ? 'success' : ($loan_guarantor->status == 'pending' ? 'warning' : 'secondary')}}">
                        {{ucfirst($loan_guarantor->status)}} {{trans_choice('loan::general.guarantor',1)}}
                    </div>

                    @if(!empty($loan_guarantor->guaranteed_amount))
                        <div style="margin-top: 10px;">
                            <small class="text-muted">Amount Guaranteed</small><br>
                            <strong style="font-size: 18px; color: #2c3e50;">{{number_format($loan_guarantor->guaranteed_amount, 2)}}</strong>
                        </div>
                    @endif
            </div>

            <!-- Contact Information Card -->
            <div class="card info-card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fa fa-address-book"></i> Contact Information</h5>
                </div>
                <div class="card-body p-0">
                    <ul class="info-list">
                        <li>
                            <span class="info-label">{{trans_choice('client::general.relationship',1)}}</span>
                            <span class="info-value">
                                @if(!empty($loan_guarantor->client_relationship))
                                    {{$loan_guarantor->client_relationship->name}}
                                @endif
                            </span>
                        </li>
                        <li>
                            <span class="info-label">{{trans_choice('core::general.mobile',1)}}</span>
                            <span class="info-value">{{$loan_guarantor->mobile}}</span>
                        </li>
                        <li>
                            <span class="info-label">{{trans_choice('core::general.email',1)}}</span>
                            <span class="info-value">{{$loan_guarantor->email}}</span>
                        </li>
                        <li>
                            <span class="info-label">{{trans_choice('core::general.dob',1)}}</span>
                            <span class="info-value">{{$loan_guarantor->dob}}</span>
                        </li>
                        <li>
                            <span class="info-label">{{trans_choice('core::general.gender',1)}}</span>
                            <span class="info-value">
                                @if($loan_guarantor->gender=='male')
                                    {{trans_choice('core::general.male',1)}}
                                @endif
                                @if($loan_guarantor->gender=='female')
                                    {{trans_choice('core::general.female',1)}}
                                @endif
                                @if($loan_guarantor->gender=='unspecified')
                                    {{trans_choice('core::general.unspecified',1)}}
                                @endif
                                @if($loan_guarantor->gender=='other')
                                    {{trans_choice('core::general.other',1)}}
                                @endif
                            </span>
                        </li>
                        <li>
                            <span class="info-label">{{trans_choice('client::general.marital_status',1)}}</span>
                            <span class="info-value">
                                @if($loan_guarantor->marital_status=='single')
                                    {{trans_choice('client::general.single',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='married')
                                    {{trans_choice('client::general.married',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='divorced')
                                    {{trans_choice('client::general.divorced',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='widowed')
                                    {{trans_choice('client::general.widowed',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='other')
                                    {{trans_choice('client::general.other',1)}}
                                @endif
                                @if($loan_guarantor->marital_status=='unspecified')
                                    {{trans_choice('core::general.unspecified',1)}}
                                @endif
                            </span>
                        </li>
                        <li>
                            <span class="info-label">{{trans_choice('core::general.zip',1)}}</span>
                            <span class="info-value">{{$loan_guarantor->zip}}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Additional Details Card -->
            <div class="card info-card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fa fa-info-circle"></i> {{trans_choice('core::general.extra',1)}} {{trans_choice('core::general.detail',2)}}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong><i class="fa fa-map-marker margin-r-5"></i> {{trans_choice('core::general.address',1)}}</strong>
                        <p class="text-muted mt-2">
                            {{$loan_guarantor->address}}<br>
                            @if(!empty($loan_guarantor->country))
                                {{$loan_guarantor->country->name}}
                            @endif
                        </p>
                    </div>

                    <div class="mb-3">
                        <strong><i class="fa fa-file-text-o margin-r-5"></i> {{trans_choice('core::general.note',2)}}</strong>
                        <p class="text-muted mt-2">{{$loan_guarantor->notes}}</p>
                    </div>

                    @if($custom_fields->count() > 0)
                        <div class="mb-3">
                            <strong><i class="fa fa-list margin-r-5"></i> {{trans_choice('customfield::general.custom_field',2)}}</strong>
                            <div class="row mt-3">
                                @foreach($custom_fields as $custom_field)
                                    <?php
                                    $custom_field_meta = \Modules\CustomField\Entities\CustomFieldMeta::where('category', 'add_guarantor')
                                        ->where('parent_id', $loan_guarantor->id)
                                        ->where('custom_field_id', $custom_field->id)
                                        ->first();
                                    ?>
                                    @if(!empty($custom_field_meta))
                                        <div class="col-md-6">
                                            <div class="custom-field-item">
                                                <div class="custom-field-label">{{$custom_field->name}}</div>
                                                <div class="custom-field-value">
                                                    @if($custom_field->type == 'file' && !empty($custom_field_meta->value))
                                                        <a href="{{asset('storage/uploads/custom_fields/'.$custom_field_meta->value)}}"
                                                           target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fa fa-download"></i> {{$custom_field_meta->value}}
                                                        </a>
                                                    @elseif($custom_field->type == 'date' && !empty($custom_field_meta->value))
                                                        <span class="text-muted">
                                                            <i class="fa fa-calendar"></i> {{date('Y-m-d', strtotime($custom_field_meta->value))}}
                                                        </span>
                                                    @elseif($custom_field->type == 'checkbox')
                                                        <span class="badge badge-{{$custom_field_meta->value ? 'success' : 'secondary'}}">
                                                            {{$custom_field_meta->value ? trans_choice('core::general.yes',1) : trans_choice('core::general.no',1)}}
                                                        </span>
                                                    @else
                                                        <span class="text-info">{{$custom_field_meta->value}}</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <!-- /.col -->

        <div class="col-md-8">
            <!-- Loan Information Card -->
            @if(!empty($loan_guarantor->loan))
            <div class="card info-card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fa fa-money"></i> {{trans_choice('loan::general.loan',1)}} Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>{{trans_choice('loan::general.loan',1)}} {{trans_choice('core::general.id',1)}}:</strong></td>
                                    <td>
                                        <a href="{{url('loan/'.$loan_guarantor->loan->id.'/show')}}">
                                            #{{$loan_guarantor->loan->id}}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('loan::general.product',1)}}:</strong></td>
                                    <td>
                                        @if(!empty($loan_guarantor->loan->loan_product))
                                            {{$loan_guarantor->loan->loan_product->name}}
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.amount',1)}}:</strong></td>
                                    <td>{{number_format($loan_guarantor->loan->principal, 2)}}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.status',1)}}:</strong></td>
                                    <td>
                                        <span class="label label-{{$loan_guarantor->loan->status == 'active' ? 'success' : ($loan_guarantor->loan->status == 'pending' ? 'warning' : 'default')}}">
                                            {{ucfirst($loan_guarantor->loan->status)}}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Client Name</strong></td>
                                    <td>
                                        @if(!empty($loan_guarantor->loan->client))
                                            <a href="{{url('client/'.$loan_guarantor->loan->client->id.'/show')}}">
                                                {{$loan_guarantor->loan->client->first_name}} {{$loan_guarantor->loan->client->last_name}}
                                            </a>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Disbursedd Date</strong></td>
                                    <td>{{$loan_guarantor->loan->disbursed_on_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Maturity Date</strong></td>
                                    <td>{{$loan_guarantor->loan->expected_maturity_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Interest Rate</strong></td>
                                    <td>{{$loan_guarantor->loan->interest_rate}}%</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Additional Information Card -->
            <div class="card info-card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fa fa-info-circle"></i> Additional Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Loan Officer:</strong></td>
                                    <td>{{$loan_guarantor->employer}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone No.</strong></td>
                                    <td>{{$loan_guarantor->phone}}</td>
                                </tr>
                                <tr>
                                    <td><strong>City:</strong></td>
                                    <td>{{$loan_guarantor->city}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Region:</strong></td>
                                    <td>{{$loan_guarantor->state}}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-striped loan-info-table">
                                <tr>
                                    <td><strong>Created Date:</strong></td>
                                    <td>{{$loan_guarantor->created_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Joined Date:</strong></td>
                                    <td>{{$loan_guarantor->joined_date}}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.created_by',1)}}:</strong></td>
                                    <td>
                                        @if(!empty($loan_guarantor->created_by))
                                            {{$loan_guarantor->created_by->first_name}} {{$loan_guarantor->created_by->last_name}}
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{trans_choice('core::general.created_at',1)}}:</strong></td>
                                    <td>{{$loan_guarantor->created_at}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.col-md-10 -->
    </div>
    <!-- /.row -->
</div>
<!-- /.container-fluid -->
@endsection
@section('scripts')
    <script>
        $('.data-table').DataTable({
            "language": {
                "lengthMenu": "{{ trans('general.lengthMenu') }}",
                "zeroRecords": "{{ trans('general.zeroRecords') }}",
                "info": "{{ trans('general.info') }}",
                "infoEmpty": "{{ trans('general.infoEmpty') }}",
                "search": "{{ trans('general.search') }}",
                "infoFiltered": "{{ trans('general.infoFiltered') }}",
                "paginate": {
                    "first": "{{ trans('general.first') }}",
                    "last": "{{ trans('general.last') }}",
                    "next": "{{ trans('general.next') }}",
                    "previous": "{{ trans('general.previous') }}"
                },
                "columnDefs": [
                    {"orderable": false, "targets": 0}
                ]
            },
            responsive: true
        });
    </script>
@endsection
