{"__meta": {"id": "Xdb4818aa13196125701e03a43e316ace", "datetime": "2025-08-08 17:48:09", "utime": 1754668089.00194, "method": "GET", "uri": "/annex/public/loan/796/show", "ip": "**************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754668087.913275, "end": 1754668089.001977, "duration": 1.0887019634246826, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1754668087.913275, "relative_start": 0, "end": **********.626806, "relative_end": **********.626806, "duration": 0.7135310173034668, "duration_str": "714ms", "params": [], "collector": null}, {"label": "Application", "start": **********.628269, "relative_start": 0.714993953704834, "end": 1754668089.00198, "relative_end": 3.0994415283203125e-06, "duration": 0.37371110916137695, "duration_str": "374ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 47874000, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "loan::themes.adminlte.loan.show (\\Modules\\Loan\\Resources\\views\\themes\\adminlte\\loan\\show.blade.php)", "param_count": 4, "params": ["loan", "users", "payment_types", "custom_fields"], "type": "blade"}, {"name": "core::layouts.master (\\themes\\adminlte\\views\\layouts\\master.blade.php)", "param_count": 57, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan", "users", "payment_types", "custom_fields", "__currentLoopData", "key", "loop", "balance", "timely_repayments", "principal", "principal_waived", "principal_paid", "principal_written_off", "principal_outstanding", "principal_overdue", "interest", "interest_waived", "interest_paid", "interest_written_off", "interest_outstanding", "interest_overdue", "fees", "fees_waived", "fees_paid", "fees_written_off", "fees_outstanding", "fees_overdue", "penalties", "penalties_waived", "penalties_paid", "penalties_written_off", "penalties_outstanding", "penalties_overdue", "arrears_days", "arrears_amount", "arrears_last_schedule", "custom_field", "field", "count", "total_days", "total_principal", "total_interest", "total_fees", "total_penalties", "total_due", "total_paid", "total_outstanding", "days", "due", "paid", "outstanding"], "type": "blade"}, {"name": "core::partials.top_nav (\\themes\\adminlte\\views\\partials\\top_nav.blade.php)", "param_count": 57, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan", "users", "payment_types", "custom_fields", "__currentLoopData", "key", "loop", "balance", "timely_repayments", "principal", "principal_waived", "principal_paid", "principal_written_off", "principal_outstanding", "principal_overdue", "interest", "interest_waived", "interest_paid", "interest_written_off", "interest_outstanding", "interest_overdue", "fees", "fees_waived", "fees_paid", "fees_written_off", "fees_outstanding", "fees_overdue", "penalties", "penalties_waived", "penalties_paid", "penalties_written_off", "penalties_outstanding", "penalties_overdue", "arrears_days", "arrears_amount", "arrears_last_schedule", "custom_field", "field", "count", "total_days", "total_principal", "total_interest", "total_fees", "total_penalties", "total_due", "total_paid", "total_outstanding", "days", "due", "paid", "outstanding"], "type": "blade"}, {"name": "core::menu.admin (\\themes\\adminlte\\views\\menu\\admin.blade.php)", "param_count": 57, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan", "users", "payment_types", "custom_fields", "__currentLoopData", "key", "loop", "balance", "timely_repayments", "principal", "principal_waived", "principal_paid", "principal_written_off", "principal_outstanding", "principal_overdue", "interest", "interest_waived", "interest_paid", "interest_written_off", "interest_outstanding", "interest_overdue", "fees", "fees_waived", "fees_paid", "fees_written_off", "fees_outstanding", "fees_overdue", "penalties", "penalties_waived", "penalties_paid", "penalties_written_off", "penalties_outstanding", "penalties_overdue", "arrears_days", "arrears_amount", "arrears_last_schedule", "custom_field", "field", "count", "total_days", "total_principal", "total_interest", "total_fees", "total_penalties", "total_due", "total_paid", "total_outstanding", "days", "due", "paid", "outstanding"], "type": "blade"}, {"name": "core::partials.flash.message (\\themes\\adminlte\\views\\partials\\flash\\message.blade.php)", "param_count": 57, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan", "users", "payment_types", "custom_fields", "__currentLoopData", "key", "loop", "balance", "timely_repayments", "principal", "principal_waived", "principal_paid", "principal_written_off", "principal_outstanding", "principal_overdue", "interest", "interest_waived", "interest_paid", "interest_written_off", "interest_outstanding", "interest_overdue", "fees", "fees_waived", "fees_paid", "fees_written_off", "fees_outstanding", "fees_overdue", "penalties", "penalties_waived", "penalties_paid", "penalties_written_off", "penalties_outstanding", "penalties_overdue", "arrears_days", "arrears_amount", "arrears_last_schedule", "custom_field", "field", "count", "total_days", "total_principal", "total_interest", "total_fees", "total_penalties", "total_due", "total_paid", "total_outstanding", "days", "due", "paid", "outstanding"], "type": "blade"}, {"name": "core::partials.footer (\\themes\\adminlte\\views\\partials\\footer.blade.php)", "param_count": 57, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "loan", "users", "payment_types", "custom_fields", "__currentLoopData", "key", "loop", "balance", "timely_repayments", "principal", "principal_waived", "principal_paid", "principal_written_off", "principal_outstanding", "principal_overdue", "interest", "interest_waived", "interest_paid", "interest_written_off", "interest_outstanding", "interest_overdue", "fees", "fees_waived", "fees_paid", "fees_written_off", "fees_outstanding", "fees_overdue", "penalties", "penalties_waived", "penalties_paid", "penalties_written_off", "penalties_outstanding", "penalties_overdue", "arrears_days", "arrears_amount", "arrears_last_schedule", "custom_field", "field", "count", "total_days", "total_principal", "total_interest", "total_fees", "total_penalties", "total_due", "total_paid", "total_outstanding", "days", "due", "paid", "outstanding"], "type": "blade"}]}, "route": {"uri": "GET loan/{id}/show", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanController@show", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:800-809"}, "queries": {"nb_statements": 35, "nb_failed_statements": 0, "accumulated_duration": 0.05615, "accumulated_duration_str": "56.15ms", "statements": [{"sql": "select * from `users` where `id` = 9 limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.01326, "duration_str": "13.26ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 9 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.00492, "duration_str": "4.92ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 9 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select * from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User' and `name` != 'client')", "type": "query", "params": [], "bindings": ["Modules\\User\\Entities\\User", "client"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>!=</code> operator is not standard. Use the <code>&lt;&gt;</code> operator to test for inequality instead."], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 804}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00155, "duration_str": "1.55ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:804", "connection": "annex"}, {"sql": "select * from `payment_types` where `active` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 805}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:805", "connection": "annex"}, {"sql": "select * from `loans` where `loans`.`id` = '796' limit 1", "type": "query", "params": [], "bindings": ["796"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_repayment_schedules` where `loan_repayment_schedules`.`loan_id` in (796) order by `due_date` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00211, "duration_str": "2.11ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_transactions` where `loan_transactions`.`loan_id` in (796) order by `submitted_on` asc, `id` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_linked_charges` where `loan_linked_charges`.`loan_id` in (796)", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `clients` where `clients`.`id` in (133)", "type": "query", "params": [], "bindings": ["133"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0016799999999999999, "duration_str": "1.68ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_products` where `loan_products`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00175, "duration_str": "1.75ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_notes` where `loan_notes`.`loan_id` in (796) order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_guarantors` where `loan_guarantors`.`loan_id` in (796)", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00135, "duration_str": "1.35ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_files` where `loan_files`.`loan_id` in (796)", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `loan_collateral` where `loan_collateral`.`loan_id` in (796)", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 806}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:806", "connection": "annex"}, {"sql": "select * from `custom_fields` where `category` = 'add_loan' and `active` = 1", "type": "query", "params": [], "bindings": ["add_loan", "1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 807}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:807", "connection": "annex"}, {"sql": "select * from `currencies` where `currencies`.`id` = 1 and `currencies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1089}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "view::loan::themes.adminlte.loan.show:1089", "connection": "annex"}, {"sql": "select * from `users` where `users`.`id` = 10 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1098}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "view::loan::themes.adminlte.loan.show:1098", "connection": "annex"}, {"sql": "select * from `loan_purposes` where `loan_purposes`.`id` = 4 and `loan_purposes`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1107}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "view::loan::themes.adminlte.loan.show:1107", "connection": "annex"}, {"sql": "select * from `funds` where `funds`.`id` = 1 and `funds`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1116}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "view::loan::themes.adminlte.loan.show:1116", "connection": "annex"}, {"sql": "select * from `loan_transaction_processing_strategies` where `loan_transaction_processing_strategies`.`id` = 1 and `loan_transaction_processing_strategies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1257}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "view::loan::themes.adminlte.loan.show:1257", "connection": "annex"}, {"sql": "select * from `users` where `users`.`id` = 10 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1365}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "view::loan::themes.adminlte.loan.show:1365", "connection": "annex"}, {"sql": "select * from `users` where `users`.`id` = 3 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1378}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "view::loan::themes.adminlte.loan.show:1378", "connection": "annex"}, {"sql": "select * from `users` where `users`.`id` = 9 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "loan::themes.adminlte.loan.show", "line": 1391}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "view::loan::themes.adminlte.loan.show:1391", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where `category` = 'add_loan' and `parent_id` = 796 and `custom_field_id` = 8 limit 1", "type": "query", "params": [], "bindings": ["add_loan", "796", "8"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\CustomField\\Helpers\\general_helper.php", "line": 17}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0048, "duration_str": "4.8ms", "stmt_id": "\\Modules\\CustomField\\Helpers\\general_helper.php:17", "connection": "annex"}, {"sql": "select * from `custom_fields_meta` where `category` = 'add_loan' and `parent_id` = 796 and `custom_field_id` = 15 limit 1", "type": "query", "params": [], "bindings": ["add_loan", "796", "15"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\CustomField\\Helpers\\general_helper.php", "line": 17}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00495, "duration_str": "4.95ms", "stmt_id": "\\Modules\\CustomField\\Helpers\\general_helper.php:17", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 30}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::partials.top_nav", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "view::core::partials.top_nav:31", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_logo' limit 1", "type": "query", "params": [], "bindings": ["core.company_logo"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 3}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "view::core::menu.admin:3", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_name' limit 1", "type": "query", "params": [], "bindings": ["core.company_name"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 8}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "view::core::menu.admin:8", "connection": "annex"}, {"sql": "select * from `menus` where `is_parent` = 1 order by `menu_order` asc", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}, {"sql": "select * from `menus` where `menus`.`parent_id` in (1, 9, 10, 12, 13, 17, 18, 21, 24, 28, 31, 36, 37, 44, 49, 54, 58, 61, 66, 71) order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}]}, "models": {"data": {"Modules\\Core\\Entities\\Menu": 73, "Modules\\Core\\Entities\\PaymentType": 2, "Modules\\Loan\\Entities\\Loan": 1, "Modules\\Loan\\Entities\\LoanRepaymentSchedule": 16, "Modules\\Loan\\Entities\\LoanTransaction": 5, "Modules\\Loan\\Entities\\LoanLinkedCharge": 2, "Modules\\Client\\Entities\\Client": 1, "Modules\\Loan\\Entities\\LoanProduct": 1, "Spatie\\Permission\\Models\\Role": 1, "Modules\\Loan\\Entities\\LoanGuarantor": 1, "Modules\\CustomField\\Entities\\CustomField": 2, "Modules\\Core\\Entities\\Currency": 1, "Modules\\Loan\\Entities\\LoanPurpose": 1, "Modules\\Loan\\Entities\\Fund": 1, "Modules\\Loan\\Entities\\LoanTransactionProcessingStrategy": 1, "Modules\\CustomField\\Entities\\CustomFieldMeta": 2, "Modules\\Setting\\Entities\\Setting": 2, "Modules\\Loan\\Entities\\LoanFile": 1, "Modules\\User\\Entities\\User": 13}, "count": 127}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<PERSON>@mjy3.com\"\n  \"user\" => array:25 [\n    \"id\" => 9\n    \"created_by_id\" => null\n    \"branch_id\" => null\n    \"name\" => \"\"\n    \"username\" => null\n    \"email\" => \"<PERSON>@mjy3.com\"\n    \"email_verified_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"<PERSON>\"\n    \"phone\" => \"+233555928981\"\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"male\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => \"This access is given for the purpose of internal functions only\"\n    \"photo\" => \"kI83IU45zpa8IywCrNXJY6rulxWk3OSLHuGt0dCg.jpeg\"\n    \"created_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"updated_at\" => \"2025-01-17T08:13:41.000000Z\"\n    \"full_name\" => \"<PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 4\n        \"is_system\" => 0\n        \"name\" => \"Loan Officer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"updated_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 9\n          \"role_id\" => 4\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n    \"unread_notifications\" => []\n  ]\n]", "api": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 80, "messages": [{"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1278842266 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278842266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754179}, {"message": "[\n  ability => loan.loans.transactions.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1158167989 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">loan.loans.transactions.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158167989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.80806}, {"message": "[\n  ability => loan.loans.disburse_loan,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-634460955 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.disburse_loan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634460955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.809472}, {"message": "[ability => loan.loans.edit, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-58165946 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">loan.loans.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58165946\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.81078}, {"message": "[\n  ability => loan.loans.charges.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1337491302 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.charges.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337491302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.81212}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-386647870 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-386647870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.813466}, {"message": "[\n  ability => loan.loans.write_off_loan,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1923094267 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.write_off_loan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923094267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.814768}, {"message": "[\n  ability => loan.loans.reschedule_loan,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-508184291 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">loan.loans.reschedule_loan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508184291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.816027}, {"message": "[ability => loan.loans.edit, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1219118940 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">loan.loans.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219118940\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.817276}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-662738599 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662738599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.819264}, {"message": "[\n  ability => loan.loans.write_off_loan,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1506409562 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.write_off_loan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1506409562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.822834}, {"message": "[\n  ability => loan.loans.reschedule_loan,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-643046121 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">loan.loans.reschedule_loan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643046121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.824233}, {"message": "[\n  ability => loan.loans.transactions.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-492835954 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">loan.loans.transactions.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492835954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.837533}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-103496447 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103496447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.839142}, {"message": "[ability => loan.loans.files.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-25776476 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">loan.loans.files.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25776476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.840574}, {"message": "[\n  ability => loan.loans.collateral.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1128862479 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">loan.loans.collateral.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128862479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.842036}, {"message": "[\n  ability => loan.loans.guarantors.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1885426116 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">loan.loans.guarantors.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885426116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.844714}, {"message": "[ability => loan.loans.notes.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-701554848 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">loan.loans.notes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701554848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.846214}, {"message": "[\n  ability => loan.loans.transactions.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-623038261 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">loan.loans.transactions.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623038261\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.875004}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-296191187 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296191187\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87745}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1920181080 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920181080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878781}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1502764296 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502764296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880258}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1626400706 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626400706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.881546}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1258766277 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258766277\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.883383}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-136905176 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136905176\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.884827}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-599245583 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599245583\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886543}, {"message": "[\n  ability => loan.loans.charges.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1553006386 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.charges.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1553006386\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.888618}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-627549694 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627549694\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.891472}, {"message": "[\n  ability => loan.loans.transactions.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-727136056 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.transactions.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727136056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.89301}, {"message": "[ability => loan.loans.files.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1934193319 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">loan.loans.files.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934193319\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.894389}, {"message": "[\n  ability => loan.loans.files.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1437110574 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">loan.loans.files.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437110574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.895647}, {"message": "[ability => loan.loans.files.edit, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1308663841 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">loan.loans.files.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308663841\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.897043}, {"message": "[\n  ability => loan.loans.files.destroy,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2077440211 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.files.destroy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077440211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.898582}, {"message": "[\n  ability => loan.loans.collateral.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1985542978 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">loan.loans.collateral.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985542978\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.900125}, {"message": "[\n  ability => loan.loans.collateral.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-314650568 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.collateral.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314650568\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.901435}, {"message": "[\n  ability => loan.loans.guarantors.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1952517464 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"27 characters\">loan.loans.guarantors.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952517464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.903189}, {"message": "[\n  ability => loan.loans.guarantors.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-243264758 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"28 characters\">loan.loans.guarantors.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243264758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.905399}, {"message": "[\n  ability => loan.loans.guarantors.edit,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1221839113 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">loan.loans.guarantors.edit</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221839113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.906906}, {"message": "[\n  ability => loan.loans.guarantors.destroy,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2002837791 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">loan.loans.guarantors.destroy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002837791\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.90813}, {"message": "[ability => loan.loans.notes.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1430836667 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">loan.loans.notes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430836667\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.909333}, {"message": "[\n  ability => loan.loans.notes.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1005732296 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">loan.loans.notes.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005732296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.910522}, {"message": "[ability => dashboard.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dashboard.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.933483}, {"message": "[\n  ability => accounting.chart_of_accounts.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"34 characters\">accounting.chart_of_accounts.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.935358}, {"message": "[\n  ability => accounting.journal_entries.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">accounting.journal_entries.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.938786}, {"message": "[ability => branch.branches.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">branch.branches.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.940395}, {"message": "[ability => branch.branches.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2019221409 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">branch.branches.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019221409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.941785}, {"message": "[ability => client.clients.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-192513837 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">client.clients.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192513837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.943088}, {"message": "[ability => client.clients.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1269725412 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">client.clients.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269725412\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.94439}, {"message": "[ability => payroll.payroll.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-658363622 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll.payroll.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658363622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.946021}, {"message": "[\n  ability => communication.campaigns.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1864654802 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">communication.campaigns.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864654802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.947364}, {"message": "[\n  ability => communication.campaigns.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-862704870 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">communication.campaigns.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862704870\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.948654}, {"message": "[\n  ability => communication.logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1010165360 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">communication.logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010165360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.949934}, {"message": "[\n  ability => communication.sms_gateways.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-9545935 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">communication.sms_gateways.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9545935\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.951207}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1579208574 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579208574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.952571}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1807677402 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807677402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.955384}, {"message": "[ability => loan.loans.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1861840196 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861840196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.957318}, {"message": "[\n  ability => loan.loans.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1973825096 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973825096\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.958714}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-385599686 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385599686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.959994}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-712384608 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712384608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.961288}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-202388616 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202388616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.962577}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1967922294 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967922294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.963848}, {"message": "[ability => reports.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-768522292 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reports.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768522292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965149}, {"message": "[ability => expense.expenses.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-24346066 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">expense.expenses.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24346066\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.966443}, {"message": "[ability => savings.savings.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1677002105 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">savings.savings.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677002105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.967762}, {"message": "[ability => savings.savings.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1164233743 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">savings.savings.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164233743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.969051}, {"message": "[\n  ability => savings.savings.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-60822898 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">savings.savings.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60822898\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.972047}, {"message": "[\n  ability => savings.savings.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1141734250 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">savings.savings.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141734250\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.973554}, {"message": "[\n  ability => customfield.custom_fields.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177488852 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">customfield.custom_fields.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177488852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.974935}, {"message": "[\n  ability => customfield.custom_fields.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-428408620 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customfield.custom_fields.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428408620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.976212}, {"message": "[ability => income.income.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1065717545 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">income.income.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065717545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.977475}, {"message": "[ability => user.users.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1756658241 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.users.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756658241\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.97877}, {"message": "[ability => user.users.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-324848758 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">user.users.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324848758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.980037}, {"message": "[ability => user.roles.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2125636788 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.roles.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125636788\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.981304}, {"message": "[ability => core.modules.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1013606106 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">core.modules.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013606106\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.982586}, {"message": "[ability => asset.assets.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-764870176 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">asset.assets.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764870176\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.983824}, {"message": "[ability => share.shares.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-677873113 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">share.shares.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677873113\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.985135}, {"message": "[ability => setting.setting.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-430170199 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430170199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.986649}, {"message": "[ability => core.menu.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1860750362 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">core.menu.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860750362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.988874}, {"message": "[ability => core.themes.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2007402590 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">core.themes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007402590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.990412}, {"message": "[\n  ability => activitylog.activity_logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1734276224 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">activitylog.activity_logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734276224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.991846}]}, "session": {"_token": "FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://*************/annex/public/loan/796/show\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "9", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://*************/annex/public/_debugbar/telescope/9f960cd0-8154-4f7b-b85a-239daa7795d6\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan/796/show", "status_code": "<pre class=sf-dump id=sf-dump-2021612545 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2021612545\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-436003300 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-436003300\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-763295722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-763295722\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1982164147 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">http://*************/annex/public/loan/796/repayment/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IlBGdVQ4VWxoOWJ1VjRmcS9ZTkRxZ1E9PSIsInZhbHVlIjoiOTJEdmhxeC96NURja3JUeDlGR1lBV1JGeEkyNnBzc0RBNzdLYkVYUUd2SnVNTjdlUW1NcXZYTlhqSmxIL2VGcEE3RmY5OUZHNXBLdFZSWHo0WTRKMlQvUjg2dmtjMXdpQ1ZIdjFXQUxKZkpSaUpacWZpTjNXcTJ0N242UkpaYVEiLCJtYWMiOiIwMjNjOTQ2M2EyMTY0ODQ5ZjAxNGZmMTY1MWIzY2FmODY3ZDJkOTNmNzIzNmY0M2MxYmMxOGFmZTcyNzZjZjJkIn0%3D; mjy3_micro_loans_session=eyJpdiI6Im8zZktuOEFsQUpYSVRaVzgxMmNZZ2c9PSIsInZhbHVlIjoidzh5NDl6Z0JsenZISTJjT0pEd0VYNXhDTVduQ2I4SzZTamI0RnpHRjJmNkhieHdUWDlLVUVsbEJ2RkFLV2o1NnJCMUljYXVPdjBTR3ZDb3ZKYWxEUmZaYUJsWWV5cDNjVldweURUbFhtZ0ZmVGtmN3RtbGRqT2VLYU1JSTlNSzkiLCJtYWMiOiJkMDAzNWY0ZTk4ZDU1OTI2YWUyOGY3NmIyOWM2MTliMzcwMjk3MDhhYjhlZTZjMzY0NjRlZTE1NzlmN2RkOGE1In0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982164147\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-928297742 data-indent-pad=\"  \"><span class=sf-dump-note>array:51</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"59 characters\">http://*************/annex/public/loan/796/repayment/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IlBGdVQ4VWxoOWJ1VjRmcS9ZTkRxZ1E9PSIsInZhbHVlIjoiOTJEdmhxeC96NURja3JUeDlGR1lBV1JGeEkyNnBzc0RBNzdLYkVYUUd2SnVNTjdlUW1NcXZYTlhqSmxIL2VGcEE3RmY5OUZHNXBLdFZSWHo0WTRKMlQvUjg2dmtjMXdpQ1ZIdjFXQUxKZkpSaUpacWZpTjNXcTJ0N242UkpaYVEiLCJtYWMiOiIwMjNjOTQ2M2EyMTY0ODQ5ZjAxNGZmMTY1MWIzY2FmODY3ZDJkOTNmNzIzNmY0M2MxYmMxOGFmZTcyNzZjZjJkIn0%3D; mjy3_micro_loans_session=eyJpdiI6Im8zZktuOEFsQUpYSVRaVzgxMmNZZ2c9PSIsInZhbHVlIjoidzh5NDl6Z0JsenZISTJjT0pEd0VYNXhDTVduQ2I4SzZTamI0RnpHRjJmNkhieHdUWDlLVUVsbEJ2RkFLV2o1NnJCMUljYXVPdjBTR3ZDb3ZKYWxEUmZaYUJsWWV5cDNjVldweURUbFhtZ0ZmVGtmN3RtbGRqT2VLYU1JSTlNSzkiLCJtYWMiOiJkMDAzNWY0ZTk4ZDU1OTI2YWUyOGY3NmIyOWM2MTliMzcwMjk3MDhhYjhlZTZjMzY0NjRlZTE1NzlmN2RkOGE1In0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"99 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at ************* Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"14 characters\">**************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">32517</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/annex/public/loan/796/show</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/annex/public/loan/796/show</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754668087.9133</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754668087</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928297742\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-641332604 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yPsec4PrJ2v0O17ETRQJnf9a8nwYk8R7zJ9UNPPA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641332604\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1985722622 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 15:48:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6IitKRE5oazFvZ3dPdklQNWtKZ09VNHc9PSIsInZhbHVlIjoiamplRnFwR3J4bkkvSDNnQXBoUkpReE5hQ1llTzJNelQxVnROUlk2S3EwUXduUzNkRWhiQ1JOL0hpQ2VqdVFHNitRWitQcytxYUVEckdVMnl2TjduZm94bmgwdUpSOTlzdmxISzlOdWljSlp5UmxkWWVhY0tyQzVDTmorc21VS3IiLCJtYWMiOiJiNGYzY2ExYzQ3ODFlYmVjODkxMmU1MzAwZmY5OGIyZDQ0MzRkNmI4M2NlZmVlNjFiODkyMjgyYzk5NzYxNjNlIn0%3D; expires=Sat, 09-Aug-2025 11:48:08 GMT; Max-Age=71999; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6IlNPTDhtbmJyRFpMZ2RJVE9BTmVmZGc9PSIsInZhbHVlIjoiY2d4NmI2bmRBM2FVc0RmbTZXUmRPVGIxTHBBT3RnY0RCeGpTT3BFeXZ2NmZTVVlsaEdudEIwYnh3V1hoa0xKSUtwcHZwTlR0cjJYaDJzU2oxYkRVVFU0dUJYR3pmeDBvZU1SK0FWZGZqZG5kc3VtRmpaSm4yK2xvbmU0QnpTV2MiLCJtYWMiOiI5NTg4ODRjOTJiNGU3M2NlZDA1YjdiYWU4YzAzMTg4YzZhOGQ1NzZjMzRiMWVjN2RjOTc1ODIwM2VmOWU5N2FjIn0%3D; expires=Sat, 09-Aug-2025 11:48:08 GMT; Max-Age=71999; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6IitKRE5oazFvZ3dPdklQNWtKZ09VNHc9PSIsInZhbHVlIjoiamplRnFwR3J4bkkvSDNnQXBoUkpReE5hQ1llTzJNelQxVnROUlk2S3EwUXduUzNkRWhiQ1JOL0hpQ2VqdVFHNitRWitQcytxYUVEckdVMnl2TjduZm94bmgwdUpSOTlzdmxISzlOdWljSlp5UmxkWWVhY0tyQzVDTmorc21VS3IiLCJtYWMiOiJiNGYzY2ExYzQ3ODFlYmVjODkxMmU1MzAwZmY5OGIyZDQ0MzRkNmI4M2NlZmVlNjFiODkyMjgyYzk5NzYxNjNlIn0%3D; expires=Sat, 09-Aug-2025 11:48:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6IlNPTDhtbmJyRFpMZ2RJVE9BTmVmZGc9PSIsInZhbHVlIjoiY2d4NmI2bmRBM2FVc0RmbTZXUmRPVGIxTHBBT3RnY0RCeGpTT3BFeXZ2NmZTVVlsaEdudEIwYnh3V1hoa0xKSUtwcHZwTlR0cjJYaDJzU2oxYkRVVFU0dUJYR3pmeDBvZU1SK0FWZGZqZG5kc3VtRmpaSm4yK2xvbmU0QnpTV2MiLCJtYWMiOiI5NTg4ODRjOTJiNGU3M2NlZDA1YjdiYWU4YzAzMTg4YzZhOGQ1NzZjMzRiMWVjN2RjOTc1ODIwM2VmOWU5N2FjIn0%3D; expires=Sat, 09-Aug-2025 11:48:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985722622\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1751523036 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://*************/annex/public/loan/796/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751523036\", {\"maxDepth\":0})</script>\n"}}